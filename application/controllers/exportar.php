<?php

/*
 * Exportação de dados
 */

class Exportar extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('imoveis_model');
		$this->load->model('composicoesexportacao_model');
		$this->load->helper('exportar');
		$this->load->helper('xml');
		$this->load->helper(array('adminforms'));

		$informacoes = array(
			'pagina' => 'imoveis',
			'titulo' => 'Exportação',
			'descricao' => '',
			'keywords' => 'imóveis'
		);

		$config = array(
			'informacoes' => $informacoes,
		);

		$this->data = $config;
	}

	/* ---------------------------------------------------------------------- */

	/**
	 * Exportação exclusiva feita pra axis, pro vivareal e com informações de proprietários
	 * Pegar apenas os disponíveis por padrão mas aceitar parâmetro que permita ver todos
	 */
	public function axis($imobiliaria = 136)
	{

		if ($imobiliaria = '316b44139a9742cfca5f9c640c47f008f6b73c1b') $imobiliaria = 1196; // Imobiliária Time

		$data['imobiliaria'] = $this->db
			->select('id, nome, site, telefone, estado, cidade, email')
			->select('codigo_imovelweb, codigo_zap, ddd_padrao, ativa_site')
			->select('portais_modificar_referencia')
			->from('imobiliarias')
			->where('id', $imobiliaria)
			->get()
			->result_array();
		$data['imobiliaria'] = $data['imobiliaria'][0];

		$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v1');

		if (isset($_GET['todos']) && $_GET['todos'] == '1') {
		} else {
			$this->db->where(' (M.mostrar=1 OR M.enviar_para_portais=1) ', false, false);
		}

		$data['imoveis_v'] = $this->db
			->select('I.*, U.nome AS angariador, IM.nome as imobiliaria')
			->select('B.bairro, C.cidade, T.tipo, E.edificio, TE.tipo2 AS tipo_site')
			->select('fotos_exportacao(I.id) AS fotos, M.modo')
			->select(' (SELECT GROUP_CONCAT(I3.infraestrutura SEPARATOR \'|\') FROM infraestruturasimovel I2 JOIN infraestruturas I3 ON I2.infraestrutura_id = I3.id WHERE I2.imovel_id=I.id ORDER BY I2.ordem) as infraestruturas', FALSE)
			->select(' (SELECT GROUP_CONCAT(C4.composicao SEPARATOR \'|\') FROM composicoesimovel C3 JOIN composicoes C4 ON C4.id = C3.composicao_id WHERE C3.imovel_id=I.id ORDER BY C3.ordem) as composicoes', FALSE)
			->select('P.nome, P.rg, P.cpf, P.nome_conjuge, P.ddd_telefone_residencial')
			->select('P.telefone_residencial, P.ddd_celular, P.celular, P.outros_contatos')
			->select('U.email AS angariador_email, U.telefone AS angariador_telefone')
			->from('imoveis I')
			->join('cidades C', 'C.id = I.cidade_id', 'left')
			->join('bairros B', 'B.id = I.bairro_id', 'left')
			->join('edificios E', 'E.id = I.edificio_id', 'left')
			->join('tipos T', 'T.id = I.tipo_id')
			->join('tiposexportacao TE', 'TE.tipo = T.tipo', 'left')
			->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
			->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left')
			->join('proprietarios P', 'P.id = I.proprietario_id', 'left')
			->join('usuarios U', 'U.id = I.corretor_angariador_id', 'left')
			->where('TE.site', 'v1')
			->where('IM.id', $imobiliaria)
			->distinct()
			->get()
			->result_array();

		$datetime          = new DateTime();
		$data['data_hora'] = $datetime->format('Y-m-d H:i:s');

		$this->load->view('exportar/vivareal_axis', $data);
	}

	/* ---------------------------------------------------------------------- */

	/*
	 * Exporta os imóveis da imobiliária para diversos portais */
	public function exportacao($versao = null, $chave = null)
	{

		/**
		 * Documentação para formato imóvel web
		 * 
		 * https://br.open.navent.com/api-reference/intro
		 * 
		 * Exemplos de xml:
		 * https://br.open.navent.com/xml/xml_intro
		 * 
		 * Endereço do endpoint da api:
		 * https://api-br-open.navent.com/ (Tem um testador online)
		 * User: IC-Infocenter
		 * Password: 2030999345
		 * 
		 */

		$data['ci'] = $this;
		$data['chave'] = $chave;
		if (isset($chave)) {

			//Buscando dados da imobiliária
			$data['imobiliaria'] = $this->db->select('id, nome, site, telefone, estado, cidade, email')
				->select('codigo_imovelweb, codigo_zap, ddd_padrao, ativa_site')
				->select('portais_modificar_referencia, whatsapp, creci, marcadagua_nova')
				->from('imobiliarias')
				->where('chave', $chave)
				->get()
				->result_array();
			$data['imobiliaria'] = $data['imobiliaria'][0];

			/* Dado pego para a Versão Touch e facebook */
			$data['imobiliaria']['chave'] = $chave;
			/* -------------------------------------------------------- */

			if (isset($data['imobiliaria'])) {

				$params_ven  = array('modo' => 'completo', 'tipo_venda' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);
				$params_loc  = array('modo' => 'completo', 'tipo_locacao' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);
				$params_lanc = array('modo' => 'completo', 'tipo_lancamento' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);
				$params_temp = array('modo' => 'completo', 'tipo_temporada' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);

				$site = '';
				$buscar_todos = 1;

				if ($versao == 'v1') {
					$site = 'vivareal';
					$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v1');
				} else if ($versao == 'v2') {
					$site = 'minhaprimeiracasa';
				} else if ($versao == 'v3') {
					$site = 'imovelweb';
				} else if ($versao == 'v5') {
					$site = 'chavesnamao';
				} else if ($versao == 'v6') {
					$site = 'zap';
				} else if ($versao == 'v8') {
					$site = 'imovelmagazine';
				} else if ($versao == 'v9') {
					$site = 'redeimoveis';
				} else if ($versao == 'v10') {
					$site = 'touch';
				} else if ($versao == 'v11') {
					$site = 'imoviewtv';
				} else if ($versao == 'v12') {
					$site = 'canaldoimovel';
				} else if ($versao == 'v13') {
					$site = 'chavefacil';
				} else if ($versao == 'v14') {
					$site = 'mercadolivre';
				} else if ($versao == 'v15') {
					$site = 'olx';
				} else if ($versao == 'v16') {
					$site = 'properati';
				} else if ($versao == 'v17') {
					$site = 'chavefacilnacional';
					$versao = 'v3';
				} else if ($versao == 'v18') {
					$site = 'luxury';
				} else if ($versao == 'v19') {
					$site = 'casamineira';
				} else if ($versao == 'v20') {
					$site = 'imovomap';
				} else if ($versao == 'v21') {
					$site = 'attria';
					$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v1');
				} else if ($versao == 'v22') {
					$site = 'exclusivo_axis';
				} else if ($versao == 'v23') {
					$site = 'exclusivo_axis';
				} else if ($versao == 'v24') {
					$site = 'facebook';
				} else if ($versao == 'v25') {
					$site = 'creci';
				} else if ($versao == 'v26') {
					$site = 'loft';
					$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v26');
				} else if ($versao == 'v27') {
					$site = 'redeuna';
					$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v27');
				} else if ($versao == 'v28') {
					$site = 'loft';
					$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v28');
				} else if ($versao == 'v29') {
					$site = 'buscacuritiba';
					$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v1');
				} else if ($versao == 'v30') {
					$site = 'v30';
				} else if ($versao == 'vi01') {
					$site = 'exclusivo_axis';
				} else if ($versao == 'v31') {
					$site = 'goharry';
					$data['interligacoes'] = $this->composicoesexportacao_model->get_interligacoes_composicoes('v1');
				}

				if ($site != '' && $site != 'chavefacil') {
					if ($site == 'exclusivo_axis') {

						if (isset($_GET['modo']) && $_GET['modo'] == 'todos') {
							// Buscar todos os modos de trabalhar
						} else {
							$this->db->where(' (M.mostrar=1 OR M.enviar_para_portais=1) ', false, false);
						}

						if (isset($_GET['ref']) && $_GET['ref'] != '') {
							$this->db->like('I.referencia', $_GET['ref'], 'after');
						}

						$data['imoveis_v'] = $this->db->select('I.*, P.id as proprietario_id, P.nome as proprietario, U.nome AS angariador')
							->select('P.rg, P.cpf, P.nome_conjuge, P.ddd_telefone_residencial, P.telefone_residencial')
							->select('P.ddd_celular, P.celular, P.outros_contatos, P.data_nascimento, T.tipo')
							->select('E.edificio, B.bairro as bairro, C.cidade as cidade')
							->select('U.email, U.telefone, M.modo')
							->select('fotos(I.id) as fotos', false)
							->select('arquivos_imovel_json(I.id) as arquivos', false)
							->from('imoveis I')
							->join('cidades C', 'C.id = I.cidade_id', 'left')
							->join('bairros B', 'B.id = I.bairro_id', 'left')
							->join('edificios E', 'E.id = I.edificio_id', 'left')
							->join('tipos T', 'T.id = I.tipo_id')
							->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
							->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left')
							->join('proprietarios P', 'P.id = I.proprietario_id', 'left')
							->join('usuarios U', 'U.id = I.corretor_angariador_id', 'left')
							->where('I.imobiliaria_id', $data['imobiliaria']['id'])
							->where('I.tipo_venda', 1)
							->order_by('T.tipo, valor_total DESC, aluguel DESC')
							->distinct()
							->get()
							->result_array();

						if (isset($_GET['modo']) && $_GET['modo'] == 'todos') {
							// Buscar todos os modos de trabalhar
						} else {
							$this->db->where(' (M.mostrar=1 OR M.enviar_para_portais=1) ', false, false);
						}

						if (isset($_GET['ref']) && $_GET['ref'] != '') {
							$this->db->like('I.referencia', $_GET['ref'], 'after');
						}

						$data['imoveis_l'] = $this->db->select('I.*, P.id as proprietario_id, P.nome as proprietario, U.nome AS angariador')
							->select('P.rg, P.cpf, P.nome_conjuge, P.ddd_telefone_residencial, P.telefone_residencial')
							->select('P.ddd_celular, P.celular, P.outros_contatos, P.data_nascimento, T.tipo')
							->select('E.edificio, B.bairro as bairro, C.cidade as cidade')
							->select('U.email, U.telefone, M.modo')
							->select('fotos(I.id) as fotos', false)
							->select('arquivos_imovel_json(I.id) as arquivos', false)
							->from('imoveis I')
							->join('cidades C', 'C.id = I.cidade_id', 'left')
							->join('bairros B', 'B.id = I.bairro_id', 'left')
							->join('edificios E', 'E.id = I.edificio_id', 'left')
							->join('tipos T', 'T.id = I.tipo_id')
							->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
							->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left')
							->join('proprietarios P', 'P.id = I.proprietario_id', 'left')
							->join('usuarios U', 'U.id = I.corretor_angariador_id', 'left')
							->where('I.imobiliaria_id', $data['imobiliaria']['id'])
							->where('I.tipo_locacao', 1)
							->order_by('T.tipo, valor_total DESC, aluguel DESC')
							->distinct()
							->get()
							->result_array();
					} else if ($site == 'touch' || $site == 'v30') {
						$data['imoveis_v'] = $this->imoveis_model->get_imoveis_api($params_ven);
						$data['imoveis_l'] = $this->imoveis_model->get_imoveis_api($params_loc);
						$data['imoveis_lanc'] = $this->imoveis_model->get_imoveis_api($params_lanc);
					} else if ($site == 'goharry') {
						$data['imoveis_v'] = $this->imoveis_model->get_imoveis_api(array_merge($params_ven, array('parceiros' => '1')));
						$data['imoveis_l'] = $this->imoveis_model->get_imoveis_api(array_merge($params_loc, array('parceiros' => '1')));
						$data['imoveis_lanc'] = $this->imoveis_model->get_imoveis_api(array_merge($params_lanc, array('parceiros' => '1')));
					} else {
						$this->db->select('ativo');
						$this->db->from('sites_parceiros_selecionar_todos');
						$this->db->where('site', $site);
						$this->db->where('imobiliaria_id', $data['imobiliaria']['id']);
						$this->db->where('tipo_venda', 1);
						$query = $this->db->get();
						$data['selecionar_todos'] = $query->result_array();

						if ((isset($_GET['pg']) && $_GET['pg'] == 'v') || !isset($_GET['pg'])) {
							if (isset($data['selecionar_todos'][0]) && $data['selecionar_todos'][0]['ativo'] == '1') {
								$data['imoveis_v'] = $this->imoveis_model->get_imoveis_exportacao($params_ven, $site, 1, $versao);
							} else {
								$data['imoveis_v'] = $this->imoveis_model->get_imoveis_exportacao($params_ven, $site, 0, $versao);
							}
						} else {
							$data['imoveis_v'] = array();
						}

						/* Gambiarra inserida para a imobiliária Ney Mathias (y) */
						if ($data['imobiliaria']['id'] == '34' && (isset($_GET['pg']) && $_GET['pg'] == 'v')) {
							$data['imobiliaria']['codigo_imovelweb'] = '741202';
						}

						if ($data['imobiliaria']['id'] == '34' && (isset($_GET['pg']) && $_GET['pg'] == 'l')) {
							$data['imobiliaria']['codigo_imovelweb'] = '587089';
						}
						/* -------------------------------------------------------- */

						/* Gambiarra inserida para a imobiliária Claro */
						if ($data['imobiliaria']['id'] == '73' && (isset($_GET['pg']) && $_GET['pg'] == 'v')) {
							$data['imobiliaria']['codigo_imovelweb'] = '586388';
						}

						if ($data['imobiliaria']['id'] == '73' && (isset($_GET['pg']) && $_GET['pg'] == 'l')) {
							$data['imobiliaria']['codigo_imovelweb'] = '739292';
						}
						/* -------------------------------------------------------- */

						/* Gambiarra inserida para a imobiliária Axis */
						if ($data['imobiliaria']['id'] == '136' && (isset($_GET['fc']) && $_GET['fc'] == '1')) {
							$data['imobiliaria']['codigo_imovelweb'] = '15767012';
						}

						if ($data['imobiliaria']['id'] == '136' && (isset($_GET['fc']) && $_GET['fc'] == '0')) {
							$data['imobiliaria']['codigo_imovelweb'] = '15737468';
						}
						/* -------------------------------------------------------- */

						$this->db->select('ativo');
						$this->db->from('sites_parceiros_selecionar_todos');
						$this->db->where('site', $site);
						$this->db->where('imobiliaria_id', $data['imobiliaria']['id']);
						$this->db->where('tipo_locacao', 1);
						$query = $this->db->get();
						$data['selecionar_todos'] = $query->result_array();

						if ((isset($_GET['pg']) && $_GET['pg'] == 'l') || !isset($_GET['pg'])) {
							if (isset($data['selecionar_todos'][0]) && $data['selecionar_todos'][0]['ativo'] == '1') {
								$data['imoveis_l'] = $this->imoveis_model->get_imoveis_exportacao($params_loc, $site, 1, $versao);
							} else {
								$data['imoveis_l'] = $this->imoveis_model->get_imoveis_exportacao($params_loc, $site, 0, $versao);
							}
						} else {
							$data['imoveis_l'] = array();
						}

						$this->db->select('ativo');
						$this->db->from('sites_parceiros_selecionar_todos');
						$this->db->where('site', $site);
						$this->db->where('imobiliaria_id', $data['imobiliaria']['id']);
						$this->db->where('tipo_lancamento', 1);
						$query = $this->db->get();
						$data['selecionar_todos'] = $query->result_array();

						if ((isset($_GET['pg']) && $_GET['pg'] == 'lanc') || !isset($_GET['pg'])) {
							if (isset($data['selecionar_todos'][0]) && $data['selecionar_todos'][0]['ativo'] == '1' || $data['imobiliaria']['id'] == 49) {
								$data['imoveis_lanc'] = $this->imoveis_model->get_imoveis_exportacao($params_lanc, $site, 1, $versao);
							} else {
								$data['imoveis_lanc'] = $this->imoveis_model->get_imoveis_exportacao($params_lanc, $site, 0, $versao);
							}
						} else {
							$data['imoveis_lanc'] = array();
						}

						$this->db->select('ativo');
						$this->db->from('sites_parceiros_selecionar_todos');
						$this->db->where('site', $site);
						$this->db->where('imobiliaria_id', $data['imobiliaria']['id']);
						$this->db->where('tipo_temporada', 1);
						$query = $this->db->get();
						$data['selecionar_todos'] = $query->result_array();

						if ((isset($_GET['pg']) && $_GET['pg'] == 'temp') || !isset($_GET['pg'])) {
							if (isset($data['selecionar_todos'][0]) && $data['selecionar_todos'][0]['ativo'] == '1' || $data['imobiliaria']['id'] == 49) {
								$data['imoveis_temp'] = $this->imoveis_model->get_imoveis_exportacao($params_temp, $site, 1, $versao);
							} else {
								$data['imoveis_temp'] = $this->imoveis_model->get_imoveis_exportacao($params_temp, $site, 0, $versao);
							}
						} else {
							$data['imoveis_temp'] = array();
						}
					}
				} else {
					$data['imoveis_v'] = $this->imoveis_model->get_imoveis_exportacao($params_ven, $site, 1, $versao);
					$data['imoveis_l'] = $this->imoveis_model->get_imoveis_exportacao($params_loc, $site, 1, $versao);
					$data['imoveis_lanc'] = $this->imoveis_model->get_imoveis_exportacao($params_lanc, $site, 1, $versao);
				}

				$datetime          = new DateTime();
				$data['data_hora'] = $datetime->format('Y-m-d H:i:s');

				if ($data['imobiliaria']['ativa_site'] == 0) {
					//Mostrar só os 3 primeiro imóveis do xml
					$data['imoveis_v'] = array_slice($data['imoveis_v'], 0, 3);
					$data['imoveis_l'] = array();
					$data['imoveis_lanc'] = array();
				}

				// Caso esteja marcado para mostrar os imóveis de parceiros nos portais
				$parceiras = $this->db->select('convidada_id')
					->from('parcerias')
					->where('imobiliaria_id', $data['imobiliaria']['id'])
					->where('enviar_para_portais', 1)
					->get()
					->result_array();

				foreach ($parceiras as $parceira) {
					$parceira_id = $parceira['convidada_id'];

					if ($parceira_id == '1268') {
						$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.parceira=I.imobiliaria_id AND ISP.referencia=I.referencia AND ISP.imobiliaria_id=' . $data['imobiliaria']['id']);
					} else {
						$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.parceira=I.imobiliaria_id AND ISP.imovel_id=I.id AND ISP.imobiliaria_id=' . $data['imobiliaria']['id']);
					}

					if (isset($versao) && $versao == 'v19') { // Casa Mineira
						$this->db->select('IWL.cidade_iw, IWL.bairro_iw');
						$this->db->join('iw_localidades IWL', 'IWL.bairro_id = I.bairro_id AND IWL.portal=\'casamineira\'', 'left');
					}

					$imoveis_parceira = $this->db->select('I.*, P.nome as proprietario, U.nome AS angariador')
						->select('IM.nome as imobiliaria, B.bairro, C.cidade, E.edificio')
						->select('T.tipo, TE.tipo2 AS tipo_site')
						->select('fotos_exportacao(I.id) AS fotos')
						->select('fotos_json(I.id) AS fotos_json')
						->select(' (SELECT GROUP_CONCAT(I3.infraestrutura SEPARATOR \'|\') FROM infraestruturasimovel I2 JOIN infraestruturas I3 ON I2.infraestrutura_id = I3.id WHERE I2.imovel_id=I.id ORDER BY I2.ordem) as infraestruturas', FALSE)
						->select(' (SELECT GROUP_CONCAT(C4.composicao SEPARATOR \'|\') FROM composicoesimovel C3 JOIN composicoes C4 ON C4.id = C3.composicao_id WHERE C3.imovel_id=I.id ORDER BY C3.ordem) as composicoes', FALSE)
						->from('imoveis I')
						->join('cidades C', 'C.id = I.cidade_id', 'left')
						->join('bairros B', 'B.id = I.bairro_id', 'left')
						->join('edificios E', 'E.id = I.edificio_id', 'left')
						->join('parcerias_tipos PT', 'PT.tipo_parceira = I.tipo_id AND PT.imobiliaria_id=' . $data['imobiliaria']['id'])
						->join('tipos T', 'PT.tipo_ligacao = T.id')
						->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
						->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left')
						->join('proprietarios P', 'P.id = I.proprietario_id', 'left')
						->join('usuarios U', 'U.id = I.corretor_angariador_id', 'left')
						->join('tiposexportacao TE', 'TE.tipo = T.tipo', 'left')
						->where('I.imobiliaria_id', $parceira_id)
						->where('M.mostrar', 1)
						->where('I.tipo_venda', 1)
						->where('TE.site', $versao)
						->distinct()
						->get()
						->result_array();

					// Nenhum imóvel da caixa encontrado nos selecionados da Área Imóveis
					if (
						$parceira_id == '1268' &&
						$data['imobiliaria']['id'] == '1282' &&
						sizeof($imoveis_parceira) < 1 &&
						$versao == 'v24'
					) {

						$query = $this->db->last_query();

						// Atualizar imóveis da Caixa novamente
						$url = 'http://**************/index.php/importacao/caixa';
						$curl = curl_init();
						curl_setopt_array($curl, array(
							CURLOPT_URL => $url,
							CURLOPT_RETURNTRANSFER => true,
							CURLOPT_ENCODING => '',
							CURLOPT_MAXREDIRS => 10,
							CURLOPT_TIMEOUT => 0,
							CURLOPT_FOLLOWLOCATION => true,
							CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
							CURLOPT_CUSTOMREQUEST => 'GET'
						));
						$resp = curl_exec($curl);

						// Reexecutar a consulta mysql, agora com os imóveis atualizados
						$imoveis_parceira = $this->db->query($query)->result_array();
					}

					if ($parceira_id == '1268') {

						$tipos_tratar = array(
							'APARTAMENTO',
							'CASA',
							'FLAT',
							'KITNET',
							'LOFT',
							'SOBRADO',
							'COBERTURA',
							'RESIDÊNCIA EM CONDOMÍNIO',
							'SOBRADO EM CONDOMÍNIO',
							'STUDIO'
						);

						foreach ($imoveis_parceira as $key => $row) {
							// Imóveis da caixa, para a imobiliária M8 - caixa
							if ($row['imobiliaria_id'] == '1268' && $data['imobiliaria']['id'] == '1271') {
								if ($row['fotos'] != '') $row['fotos'] .= '||';

								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1271/base-01.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1271/base-02.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1271/base-03.jpg';

								// Imóveis da caixa para a área imóveis
							} else if ($row['imobiliaria_id'] == '1268' && $data['imobiliaria']['id'] == '1282') {
								if ($row['fotos'] != '') $row['fotos'] .= '||';

								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/1.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/2.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/3.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/4.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/5.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/6.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/7.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/8.jpg||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1282/2/9.jpg';

								// Imóveis da caixa para a Frizon imóveis
							} else if ($row['imobiliaria_id'] == '1268' && $data['imobiliaria']['id'] == '1378') {
								if ($row['fotos'] != '') $row['fotos'] .= '||';
								$row['fotos'] .= 'https://painel.promentor.com.br/img/fotos_padrao/caixa/1378/1.jpg';
							}

							// Colocar 1 em banheiros e dormitórios pra todos os imóveis da caixa que não tenham estes campos preenchidos
							if ($row['imobiliaria_id'] == '1268') {
								if (in_array($row['tipo'], $tipos_tratar)) {

									if (!isset($row['dormitorios']) || !isset($row['banheiros'])) {
										$row['descricao'] .= '<br><br>OBS.: Confirmar com a imobiliária a quantidade de banheiros e quartos.';

										if (!isset($row['dormitorios'])) $row['dormitorios'] = 1;
										if (!isset($row['banheiros'])) $row['banheiros'] = 1;
									}
								}
							}

							// Alterar a descrição do imóveis caso seja da caixa e para a área imóveis
							if ($row['imobiliaria_id'] == '1268' && $data['imobiliaria']['id'] == '1282') {
								$row['descricao'] = '<p>Quer INVESTIR no MERCADO IMOBILIÁRIO? Não perca essa CHANCE de comprar imóveis com preços únicos e oportunidades exclusivas!</p><p>&nbsp;</p>' . $row['descricao'] . '<p>&nbsp;</p><p>ENVIE MENSAGEM AGORA (41) 99966-3908<br>E saiba como investir neste imóvel.</p><p>&nbsp;</p><p>ÁREA IMÓVEIS - Para MORAR ou INVESTIR!<br>Creci: J04175 / areaimoveis.com.br<br>Agência imobiliária credenciada CAIXA.</p><p>&nbsp;</p><p>AVISO LEGAL: A propriedade deste imóvel já está consolidada em nome da Caixa Econômica Federal. > MODALIDADES: Venda Online (leilão online) ou Venda Direta. > CONDIÇÕES DE PAGAMENTO: Estão descritas no anúncio, valores a serem confirmados no dia da proposta. > O Banco não aceita troca, permuta ou proposta de menor valor. > VISITA E FOTOS: o ideal é ir conhecer a localização e como o imóvel se encontra no momento (ver o imóvel apenas por fora), pois imóvel ocupado não é possibilitado a visita, o endereço completo está no anúncio.</p><p>&nbsp;</p>';
							}

							$imoveis_parceira[$key] = $row;
						}
					}

					$data['imoveis_v'] = array_merge($data['imoveis_v'], $imoveis_parceira);
				}

				$num_contratados = $this->db->select('imoveis_permitidos, portal')
					->from('portais_quantidades')
					->where('imobiliaria_id', $data['imobiliaria']['id'])
					->where('portal', $site)
					->get()
					->result_array();

				if (isset($num_contratados[0]['imoveis_permitidos'])) {
					$data['imoveis_v'] = array_slice($data['imoveis_v'], 0, $num_contratados[0]['imoveis_permitidos']);

					if (sizeof($data['imoveis_v']) == (int) $num_contratados[0]['imoveis_permitidos']) {
						$data['imoveis_l'] = array();
						$data['imoveis_lanc'] = array();
					}
				}

				if ($versao == 'v1') {
					$this->load->view('exportar/vivareal', $data);
				} else if ($versao == 'v2') {
					$this->load->view('exportar/minhaprimeiracasa', $data);
				} else if ($versao == 'v3') {
					$this->load->view('exportar/imovelweb', $data);
				} else if ($versao == 'v4') {
					$this->load->view('exportar/portalimoveiscuritiba', $data);
				} else if ($versao == 'v5') {
					$this->load->view('exportar/chavesnamao', $data);
				} else if ($versao == 'v6') {
					$this->load->view('exportar/zapimoveis', $data);
				} else if ($versao == 'v7') {
					$this->load->view('exportar/valuegaia', $data);
				} else if ($versao == 'v8') {
					$this->load->view('exportar/imovelmagazine', $data);
				} else if ($versao == 'v9') {
					$this->load->view('exportar/redeimoveis', $data);
				} else if ($versao == 'v10') {
					$this->load->view('exportar/touch', $data);
				} else if ($versao == 'v11') {
					$this->output
						->set_content_type('application/json')
						->set_output(json_encode(array_merge($data['imoveis_v'], $data['imoveis_l'], $data['imoveis_lanc'])));
				} else if ($versao == 'v12') {
					$this->load->view('exportar/canaldoimovel', $data);
				} else if ($versao == 'v13') {
					$this->load->view('exportar/minhaprimeiracasa', $data);
				} else if ($versao == 'v14') {
					$this->load->view('exportar/mercadolivre', $data);
				} else if ($versao == 'v15') {
					$this->load->view('exportar/olx', $data);
				} else if ($versao == 'v16') {
					$this->load->view('exportar/properati', $data);
				} else if ($versao == 'v17') {
					$this->load->view('exportar/chavefacilnacional', $data);
					$versao = 'v3';
				} else if ($versao == 'v18') {
					$this->load->view('exportar/luxury', $data);
				} else if ($versao == 'v19') {
					$this->load->view('exportar/casamineira_iw', $data);
				} else if ($versao == 'v20') {
					$this->load->view('exportar/imovomap', $data);
				} else if ($versao == 'v21') {
					$this->load->view('exportar/attria', $data);
				} else if ($versao == 'v22') {
					// Liberar apenas para as imobiliárias abaixo
					if (!in_array($data['imobiliaria']['id'], array(1548,1672))) { // Dudeque e Axis
						exit();
					}
					$this->load->view('exportar/axis', $data);
				} else if ($versao == 'v23') {
					$this->load->view('exportar/v23', $data);
				} else if ($versao == 'v24') {
					$this->load->view('exportar/facebook', $data);
				} else if ($versao == 'v25') {
					$data['imoveis'] = array_merge($data['imoveis_v'], $data['imoveis_l'], $data['imoveis_lanc']);
					$this->load->view('exportar/creci', $data);
				} else if ($versao == 'v26') {
					$this->load->view('exportar/loft', $data);
				} else if ($versao == 'v27') {
					$this->load->view('exportar/redeuna', $data);
				} else if ($versao == 'v28') {
					$this->load->view('exportar/loft', $data);
				} else if ($versao == 'v29') {

					$data['imoveis_v'] = tratarImoveisParaPortal('vivareal', $data['imoveis_v'], $data['imobiliaria']);
					$data['imoveis_lanc'] = tratarImoveisParaPortal('vivareal', $data['imoveis_lanc'], $data['imobiliaria']);
					$data['imoveis_l'] = tratarImoveisParaPortal('vivareal', $data['imoveis_l'], $data['imobiliaria']);
					$data['imoveis_temp'] = tratarImoveisParaPortal('vivareal', $data['imoveis_temp'], $data['imobiliaria']);

					$interligacoes = array();
					foreach ($data['interligacoes'] as $lig) {
						$interligacoes[mb_strtolower($lig['composicao'])] = $lig['composicao2'];
					}
					$data['interligacoes'] = $interligacoes;

					$this->load->view('exportar/buscacuritiba', $data);
				} else if ($versao == 'v30') {
					//tratar retorno do json
					$data['imoveis_v'] = tratarRetornoV30($data['imoveis_v'], $data['imobiliaria']['site'] . '/imoveis/venda/');
					$data['imoveis_l'] = tratarRetornoV30($data['imoveis_l'], $data['imobiliaria']['site'] . '/imoveis/locacao/');
					$data['imoveis_lanc'] = tratarRetornoV30($data['imoveis_lanc'], $data['imobiliaria']['site'] . '/imoveis/lancamento/');
					//print_r(array_merge($data['imoveis_v'], $data['imoveis_l'], $data['imoveis_lanc']));
					$this->output
						->set_content_type('application/json')
						->set_output(json_encode(array_merge($data['imoveis_v'], $data['imoveis_l'], $data['imoveis_lanc'])));
				} else if ($versao == 'vi01') {
					$this->load->view('exportar/axis', $data);
				} else if ($versao == 'vi02') {

					$data['usuarios'] = $this->db->select('U.*, C.cidade, N.nivel AS nivel_extenso, E.nome AS equipe')
						->select('U.endereco, U.cep, U.creci_numero, U.cargo, U.whatsapp')
						->select('U.crm, U.repasse_contato, U.ordem_lead, U.inclusao, U.alteracao')
						->select('U.gerente_id, U.login, U.apelido')
						->from('usuarios U')
						->join('cidades C', 'U.cidade_id = C.id', 'left')
						->join('niveis N', 'U.nivel = N.id', 'left')
						->join('equipes E', 'U.equipe_id = E.id', 'left')
						->where('U.imobiliaria_id', $data['imobiliaria']['id'])
						->where('U.nivel <> ', 5)
						->order_by('U.nome')
						->get()
						->result_array();

					$datetime = new DateTime();
					$data['data_hora'] = $datetime->format('Y-m-d H:i:s');

					$this->load->view('exportar/usuarios', $data);
				} else if ($versao == 'v31') {

					$data['imoveis_v'] = tratarImoveisParaPortal('vivareal', $data['imoveis_v'], $data['imobiliaria']);
					$data['imoveis_lanc'] = tratarImoveisParaPortal('vivareal', $data['imoveis_lanc'], $data['imobiliaria']);
					$data['imoveis_l'] = tratarImoveisParaPortal('vivareal', $data['imoveis_l'], $data['imobiliaria']);
					$data['imoveis_temp'] = tratarImoveisParaPortal('vivareal', $data['imoveis_temp'], $data['imobiliaria']);

					$interligacoes = array();
					foreach ($data['interligacoes'] as $lig) {
						$interligacoes[mb_strtolower($lig['composicao'])] = $lig['composicao2'];
					}
					$data['interligacoes'] = $interligacoes;

					$this->load->view('exportar/goharry', $data);
				}
			}
		}
	}

	/* ---------------------------------------------------------------------- */

	/**
	 * Exportacão padrão colibra, com todos os nossos dados
	 * 
	 */
	public function colibra($chave = null)
	{

		$formato = (isset($_GET['formato'])) ? $_GET['formato'] : 'xml';
		$secao = (isset($_GET['secao'])) ? $_GET['secao'] : 'imoveis';

		if (!isset($chave)) {
			// TODO: Mostrar a mensagem como um erro e não somente escrita na tela
			echo 'Chave da imobiliária não foi especificada';
			exit(1);
		}

		//Buscando dados da imobiliária
		$data['imobiliaria'] = $this->db->select('id, nome, site, telefone, estado, cidade, email')
			->from('imobiliarias')
			->where('chave', $chave)
			->get()
			->row();

		if ($secao == 'imoveis') {
			// Caso seja mostrar todos, colocar left no join. 
			// Neste caso não tem seleção de imóveis (ainda) então é pra mostrar todos
			$mostrar_todos_imoveis = 'left';

			if (isset($_GET['referencia']) && $_GET['referencia'] !== '') {
				$this->db->like('referencia', $_GET['referencia']);
			}

			$campos_imovel = 'I.360, I.id, I.acabamentos, I.aluguel, I.aluguel_bruto,
				I.andar,I.ano_de_construcao,I.apartamentos_por_andar,I.area_averbada,
				I.area_coberta,I.area_comum,I.area_laje,I.area_privativa,I.area_terreno,
				I.area_total,I.area_total_inicial,I.area_total_final,I.area_util_inicial,
				I.area_util_final,I.bairro_id,I.balneario_id,I.banheiros,
				I.banheiro_de_empregada,I.bonificacao,I.categoria_id,I.cep,I.chaves,
				I.chaves_em,I.cidade_id,I.circunscricao,I.cliques,I.cobertura,
				I.codigo_da_chave,I.comissao,I.como_chegar,I.complemento,
				I.condicoes_para_negociacao,I.condominio,I.condominio_fechado,
				I.construtora,I.contato_no_local,I.contato_nome,I.contato_email,
				I.contato_telefone,I.corretor_angariador_id, U.nome AS corretor_angariador_nome,
				U.telefone AS corretor_angariador_fone,
				I.data_da_baixa,
				I.data_do_cadastro,I.dependencia_de_empregada,I.descricao,
				I.descricao_do_empreendimento,I.destacar,I.distancia_do_mar,
				I.dormitorios,I.dormitorios_final,I.dormitorios_inicial,
				I.edificio_id,I.edicula,I.elevador,I.empreendimento_realizado,
				I.endereco,I.entrada,I.entrega_em,I.entre_ruas,I.escritura,
				I.esquadrias,I.estacionamento,I.estado,I.estado_de_conservacao,
				I.estagio_da_obra,I.exclusivo,I.face_do_apartamento,
				I.face_do_imovel,I.forma_de_permuta,I.formato_do_terreno,
				I.garagens,I.garagem_inicial,I.garagem_final,I.hora_de_visita,
				I.imediacoes,I.imob_codigo_imoview,I.imobiliaria_id,
				I.indicacao_fiscal,I.inicio_da_opcao,I.iptu,I.lancamento,
				I.latitude,I.litoral,I.logradouro,I.longitude,I.lote,I.matricula,
				I.medidas,I.minha_casa_minha_vida,I.mobiliado,I.mostrar_endereco,
				I.mostrar_numero,I.mostrar_complemento,I.nome_do_empreendimento,
				I.novo,I.numero,I.numero_andares,I.numero_de_pessoas,
				I.numero_do_apartamento,I.observacoes,I.observacoes_de_temporada,
				I.ocupacao,I.oferta,I.oferta_locacao,I.oferta_venda,I.pasta,
				I.pavimentos,I.pe_direito,I.periodo_minimo_no_ano_novo,
				I.periodo_minimo_no_carnaval,I.permite_permuta,I.piso,I.planta,
				I.plantao,I.pode_por_placa,I.posicao_na_quadra,I.possui_placa,
				I.prestacao,I.prestacoes_a_pagar,I.prestacoes_pagas,I.proprietario_id,
				I.quadra,I.quadras_da_avenida,I.quadras_da_praia,
				I.quantidade_de_chaves,I.referencia,I.referencia_imoview,
				I.reservado,I.reservado1,I.reservado2,I.reservado_para,
				I.restricoes_do_proprietario,I.revestimento_externo,
				I.revestimento_interno,I.sacada,I.saldo_devedor,I.seguro,
				I.status_de_venda,I.suite,I.taxa_de_limpeza_urbana,
				I.tempo_de_contrato,I.testada,I.teto,I.tipo_de_apartamento,
				I.tipo_de_lancamento,I.tipo_de_portaria,I.tipo_de_uso,I.tipo_id,
				I.tipo_lancamento,I.tipo_locacao,I.tipo_temporada,I.tipo_venda,
				I.tipo_construcao,I.topografia,I.trabalhar_como_id,
				I.valor_alta_temporada,I.valor_ano_novo,I.valor_baixa_temporada,
				I.valor_carnaval,I.valor_de_angariacao_inicial,
				I.valor_de_avaliacao,I.valor_inicial,I.valor_final,I.valor_por_m2,
				I.valor_total,I.valor_oferta_venda,I.valor_oferta_locacao,
				I.vencimento_da_opcao,I.video,I.vista_para_o_mar,I.zoneamento,
				I.tipo_iptu,I.tipo_seguro,I.total_unidades,I.pontos_fortes,
				I.alteracao,I.empresa,I.seo_slug,I.seo_titulo,I.seo_descricao,
				I.seo_slug_alterar,I.seo_descricao_alterar,I.seo_titulo_alterar,
				I.orulo_id,I.score_valor,I.score_localizacao,I.score_documentacao,
				I.score_infraestrutura,I.score_acabamento,I.score_investimento,
				I.score_consideracoes,I.score_urgencia,I.score_datalimite,
				I.score_conservacao,I.score_condominio,I.desconto_calculado,
				I.indicador_id,I.area_construida,I.internacional,
				I.score_flexibilidade,I.score_urgencia2,I.condominio_mes_base,
				I.imovel_de_rua,I.outros,I.agencia,I.corretor_galvao,
				I.semimobiliado,I.parte_cadastro,I.validacoes,I.referencia_interna,
				I.frente_mar,I.quadra_mar,I.numero_torres,I.origem';

			$data['imoveis'] = $this->db->select($campos_imovel . ',B.bairro, C.cidade, U.nome as corretor_angariado_nome')
				->select('T.tipo, E.edificio, IP.destacar')
				->select('IP.tipo_venda, IP.tipo_locacao, IP.tipo_temporada, IP.tipo_lancamento')
				->select('fotos_json(I.id) AS fotos')
				->select('infraestruturas_json(I.id) as infraestruturas', FALSE)
				->select('composicoes_json(I.id) as composicoes', FALSE)
				->from('imoveis I')
				->join('cidades C', 'C.id = I.cidade_id')
				->join('bairros B', 'B.id = I.bairro_id', 'left')
				->join('edificios E', 'E.id = I.edificio_id', 'left')
				->join('tipos T', 'T.id = I.tipo_id')
				->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id')
				->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'colibra\'', $mostrar_todos_imoveis)
				->join('usuarios U', 'U.id = I.corretor_angariador_id', 'left')
				->where(' (M.mostrar=1 OR M.enviar_para_portais=1) ', false, false)
				->where('I.imobiliaria_id', $data['imobiliaria']->id)
				->get()
				->result();

			for ($i = 0; $i < sizeof($data['imoveis']); $i++) {
				$data['imoveis'][$i]->fotos = json_decode($data['imoveis'][$i]->fotos);
				$data['imoveis'][$i]->infraestruturas = json_decode($data['imoveis'][$i]->infraestruturas);
				$data['imoveis'][$i]->composicoes = json_decode($data['imoveis'][$i]->composicoes);

				for ($j = 0; $j < sizeof($data['imoveis'][$i]->fotos); $j++) {
					$url = (substr($data['imoveis'][$i]->fotos[$j]->foto, 0, 4) == 'http') ? $data['imoveis'][$i]->fotos->foto : ENDERECO_FOTOS . 'fotosimovel/' . $data['imoveis'][$i]->id . '/' . $data['imoveis'][$i]->fotos[$j]->foto;
					$data['imoveis'][$i]->fotos[$j]->foto = $url;
				}

				$corretor_nome = $data['imoveis'][$i]->corretor_angariado_nome;
			}
		}

		if ($secao == 'tipos') {
			$data['tipos'] = $this->db->select('tipo')
				->from('tipos')
				->where('imobiliaria_id', $data['imobiliaria']->id)
				->get()
				->result();
		}

		if ($secao == 'composicoes') {
			$data['composicoes'] = $this->db->select('composicao')
				->from('composicoes')
				->where('imobiliaria_id', $data['imobiliaria']->id)
				->get()
				->result();
		}

		if ($secao == 'infraestruturas') {
			$data['infraestruturas'] = $this->db->select('infraestrutura')
				->from('infraestruturas')
				->where('imobiliaria_id', $data['imobiliaria']->id)
				->get()
				->result();
		}

		if ($secao == 'edificios') {
			$data['edificios'] = $this->db->select('edificio')
				->from('edificios')
				->where('imobiliaria_id', $data['imobiliaria']->id)
				->get()
				->result();
		}

		if ($formato == 'xml') {
			$this->load->view('exportar/colibra', $data);
		} else {

			$json = array(
				'imobiliaria' => $data['imobiliaria'],
				$secao => $data[$secao]
			);
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($json));
		}
	}

	/* ---------------------------------------------------------------------- */

	/*
	 * Usado para gerar a chave da imobiliária
	 * Entrar no endereço: /exportar/gerar_chave/imobiliaria_id - imobiliaria_nome
	 * Cadastrar o código gerado no banco (tabela imobiliarias)*/
	public function gerar_chave($valor)
	{
		$data['ci'] = $this;
		$this->load->helper('security');
		echo do_hash($valor);
	}

	/* ---------------------------------------------------------------------- */

	/*
	 * Usado para fazer a autenticação na olx
	 * Tem documentação da Library da olx em https://github.com/olxbr/olx-api-client
	 * Tem documentação em pdf no chamado 12728 */
	public function olx($imobiliaria = null)
	{
		$data['ci'] = $this;
		require 'application/libraries/olx/Client.php';
		require 'application/libraries/olx/OlxHttpRequest.php';

		$config = array(
			'client_id' => "40dfc27e1ee88923e0d376202939457490007121",
			"client_secret" => "e6aca6d154b25560150f74c7e9154c7e",
			"scope" => "autoupload",
			"redirect_uri" => "http://www.infocenterhost2.com.br/crm/index.php/exportar/olx"
		);
		$config = json_encode($config);

		$client = new \OlxApiClient\Client($config);

		if (isset($imobiliaria)) {
			//Primeira etapa da autenticação

			if (isset($_GET['olx_access_token']) && $_GET['olx_access_token'] != '') {

				$this->load->helper('file');
				$chave = read_file('imob.txt');

				$this->db->select('id, nome, site, telefone, estado, cidade, email, codigo_imovelweb, codigo_zap, ddd_padrao, ativa_site');
				$this->db->from('imobiliarias');
				$this->db->where('chave', $chave);
				$query = $this->db->get();
				$data['imobiliaria'] = $query->result_array();
				$data['imobiliaria'] = $data['imobiliaria'][0];

				$versao      = 'v15';
				$site        = 'olx';
				$params_ven  = array('modo' => 'completo', 'tipo_venda' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);
				$params_loc  = array('modo' => 'completo', 'tipo_locacao' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);
				$params_lanc = array('modo' => 'completo', 'tipo_lancamento' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);
				$params_temp = array('modo' => 'completo', 'tipo_temporada' => '1', 'imobiliaria_id' => $data['imobiliaria']['id']);

				$this->db->select('ativo');
				$this->db->from('sites_parceiros_selecionar_todos');
				$this->db->where('site', $site);
				$this->db->where('imobiliaria_id', $data['imobiliaria']['id']);
				$this->db->where('tipo_venda', 1);
				$query = $this->db->get();
				$data['selecionar_todos'] = $query->result_array();

				if (isset($data['selecionar_todos'][0]) && $data['selecionar_todos'][0]['ativo'] == '1') {
					$data['imoveis_v'] = $this->imoveis_model->get_imoveis_exportacao($params_ven, $site, 1, $versao);
				} else {
					$data['imoveis_v'] = $this->imoveis_model->get_imoveis_exportacao($params_ven, $site, 0, $versao);
				}

				$data['resposta'] = '';
				$data['arquivos'] = '';
				$count = count($data['imoveis_v']);
				$imoveis_partes = array_chunk($data['imoveis_v'], 70);
				echo '<p>Total de imóveis de venda: ' . sizeof($data['imoveis_v']) . '</p>';
				for ($i = 0; $i < sizeof($imoveis_partes); $i++) {
					$data['imoveis_v'] = $imoveis_partes[$i];
					$imoveis = $this->load->view('exportar/olx', $data, true);
					salvar_arquivo_texto($imoveis, 'olx' . $data['imobiliaria']['id'] . '_v_' . $i . '.json', false);
					$data['arquivos'] .= '<p><a href="http://www.infocenterhost2.com.br/crm/olx' . $data['imobiliaria']['id'] . '_v_' . $i . '.json">arquivo de venda ' . $i . '</a></p>';
					if ($data['resposta'] != '') $data['resposta'] .= ',';
					$data['resposta'] .= $client->call_autoupload($imoveis);
					sleep(30); // Este tempo é necessário pois se o envio for feito logo em seguida, o olx recusa
				}

				// ------------     Início da parte de locação     ------------- //

				$data['imoveis_v'] = array();

				$this->db->select('ativo');
				$this->db->from('sites_parceiros_selecionar_todos');
				$this->db->where('site', $site);
				$this->db->where('imobiliaria_id', $data['imobiliaria']['id']);
				$this->db->where('tipo_locacao', 1);
				$query = $this->db->get();
				$data['selecionar_todos'] = $query->result_array();

				if (isset($data['selecionar_todos'][0]) && $data['selecionar_todos'][0]['ativo'] == '1') {
					$data['imoveis_l'] = $this->imoveis_model->get_imoveis_exportacao($params_loc, $site, 1, $versao);
				} else {
					$data['imoveis_l'] = $this->imoveis_model->get_imoveis_exportacao($params_loc, $site, 0, $versao);
				}

				$count = count($data['imoveis_l']);
				$imoveis_partes = array_chunk($data['imoveis_l'], 70);
				echo '<p>Total de imóveis de locação: ' . sizeof($data['imoveis_l']) . '</p>';
				for ($i = 0; $i < sizeof($imoveis_partes); $i++) {
					$data['imoveis_l'] = $imoveis_partes[$i];
					$imoveis = $this->load->view('exportar/olx', $data, true);
					salvar_arquivo_texto($imoveis, 'olx' . $data['imobiliaria']['id'] . '_l_' . $i . '.json', false);
					$data['arquivos'] .= '<p><a href="http://www.infocenterhost2.com.br/crm/olx' . $data['imobiliaria']['id'] . '_l_' . $i . '.json">arquivo de locação ' . $i . '</a></p>';
					if ($data['resposta'] != '') $data['resposta'] .= ',';
					$data['resposta'] .= $client->call_autoupload($imoveis);

					if (($i + 1) < sizeof($imoveis_partes)) sleep(30); // Este tempo é necessário pois se o envio for feito logo em seguida, o olx recusa
				}

				$this->load->view('exportar/olx_resposta', $data);
			} else {
				echo '<a href="' . $client->createAuthUrl() . '">Autenticar</a>';
				salvar_arquivo_texto($imobiliaria, 'imob.txt', false);
				$_SESSION['chave'] = $imobiliaria;
			}
		} else {
			//Segunda etapa da autenticação. Já recebeu a resposta da primeira requisição

			if (!isset($_GET['code'])) {
				// Erro
			} else {
				$this->load->helper('file');
				$chave = read_file('imob.txt');

				$_SESSION['olx_access_token'] = $client->authenticate($_GET['code']);
				header('Location: http://www.infocenterhost2.com.br/crm/index.php/exportar/olx/' . $chave . '?olx_access_token=' . $_SESSION['olx_access_token']);
			}
		}
	}

	/* ---------------------------------------------------------------------- */

	public function proprietarios($chave = null) {

		if (!isset($chave)) {
			echo 'Chave da imobiliária não foi especificada';
			exit(1);
		}

		$data['imobiliaria'] = $this->db->select('id, nome, site, telefone, estado, cidade, email')
			->select('codigo_imovelweb, codigo_zap, ddd_padrao, ativa_site')
			->select('portais_modificar_referencia, whatsapp, creci, marcadagua_nova')
			->from('imobiliarias')
			->where('chave', $chave)
			->get()
			->result_array();
		$data['imobiliaria'] = $data['imobiliaria'][0];

		$this->session->set_userdata('imobiliaria_id', $data['imobiliaria']['id']);

		$this->load->model('proprietarios_model');
		$busca = array('where' => '');
		$data['proprietarios'] = $this->proprietarios_model->get_proprietarios('array', $busca);

		// Remove recontatos data for cleaner XML output
		foreach ($data['proprietarios'] as &$proprietario) {
			unset($proprietario['recontatos']);
		}

		$datetime = new DateTime();
		$data['data_hora'] = $datetime->format('Y-m-d H:i:s');

		$this->load->view('exportar/proprietarios', $data);
	}

	/* ---------------------------------------------------------------------- */

	public function clientes($chave = null) {
		if (!isset($chave)) {
			echo 'Chave da imobiliária não foi especificada';
			exit(1);
		}

		$imobiliaria = $this->db->select('id, nome, site, telefone, estado, cidade, email')
			->select('codigo_imovelweb, codigo_zap, ddd_padrao, ativa_site')
			->select('portais_modificar_referencia, whatsapp, creci, marcadagua_nova')
			->from('imobiliarias')
			->where('chave', $chave)
			->get()
			->result_array();
		$imobiliaria = $imobiliaria[0];
		$this->session->set_userdata('imobiliaria_id', $imobiliaria['id']);
		$this->session->set_userdata('nivel', 1);
		$this->session->set_userdata('order_cliente', 'id');

		$por_pagina = 1000;
		$total_clientes = $this->db->select('COUNT(*) AS total')
			->from('clientes')
			->where('imobiliaria_id', $imobiliaria['id'])
			->where('proprietario_id IS NULL', false, false)
			->get()
			->result_array();
		$total_clientes = $total_clientes[0]['total'];
		$data['total_paginas']  = ceil($total_clientes / $por_pagina);

		// Caso não tenha $_GET['pg'], mostrar um link com as páginas
		if (!isset($_GET['pg'])) {
			for ($i = 1; $i <= $data['total_paginas']; $i++) {
				echo '<p><a href="' . base_url() . 'index.php/exportar/clientes/' . $chave . '?pg=' . $i . '">Página ' . $i . '</a></p>';
			}
			exit(0);
		}

		$this->load->model('clientes_model');
		$data['clientes'] = $this->clientes_model->get_clientes_completo($imobiliaria['id']);

		$datetime = new DateTime();
		$data['data_hora'] = $datetime->format('Y-m-d H:i:s');

		$this->load->view('exportar/clientes', $data);
	}

	/* ---------------------------------------------------------------------- */

	/**
	 * Exportação de usuários em XML
	 * Acesso: /exportar/usuarios/chave_da_imobiliaria
	 * Retorna XML com dados completos dos usuários da imobiliária
	 */
	public function usuarios($chave = null) {
		if (!isset($chave)) {
			echo 'Chave da imobiliária não foi especificada';
			exit(1);
		}

		$imobiliaria = $this->db->select('id, nome, site, telefone, estado, cidade, email')
			->select('codigo_imovelweb, codigo_zap, ddd_padrao, ativa_site')
			->select('portais_modificar_referencia, whatsapp, creci, marcadagua_nova')
			->from('imobiliarias')
			->where('chave', $chave)
			->get()
			->result_array();
		$imobiliaria = $imobiliaria[0];

		$data['usuarios'] = $this->db->select('U.*, C.cidade, N.nivel AS nivel_extenso, 
			E.nome AS equipe, U2.nome AS gerente')
			->from('usuarios U')
			->join('cidades C', 'U.cidade_id = C.id', 'left')
			->join('niveis N', 'U.nivel = N.id', 'left')
			->join('equipes E', 'U.equipe_id = E.id', 'left')
			->join('usuarios U2', 'U.gerente_id = U2.id', 'left')
			->where('U.imobiliaria_id', $imobiliaria['id'])
			->order_by('U.nome')
			->get()
			->result_array();

		$datetime = new DateTime();
		$data['data_hora'] = $datetime->format('Y-m-d H:i:s');

		$this->load->view('exportar/usuarios', $data);
	}

	/* ---------------------------------------------------------------------- */

	public function cards($chave = null) {
		if (!isset($chave)) {
			echo 'Chave da imobiliária não foi especificada';
			exit(1);
		}

		$this->load->model('atendimentos_model');
		$this->load->model('clientes_model');
		$this->load->model('imoveis_model');

		$imobiliaria = $this->db->select('id, nome, site, telefone, estado, cidade, email')
			->select('codigo_imovelweb, codigo_zap, ddd_padrao, ativa_site')
			->select('portais_modificar_referencia, whatsapp, creci, marcadagua_nova')
			->from('imobiliarias')
			->where('chave', $chave)
			->get()
			->result_array();
		$imobiliaria = $imobiliaria[0];
		$this->session->set_userdata('imobiliaria_id', $imobiliaria['id']);

		$por_pagina = 100;
		$total_cards = $this->db->select('COUNT(*) AS total')
			->from('funil_leads')
			->where('imobiliaria_id', $imobiliaria['id'])
			->get()
			->result_array();
		$total_cards = $total_cards[0]['total'];
		$data['total_paginas']  = ceil($total_cards / $por_pagina);

		// Caso não tenha $_GET['pg'], mostrar um link com as páginas
		if (!isset($_GET['pg'])) {
			for ($i = 1; $i <= $data['total_paginas']; $i++) {
				echo '<p><a href="' . base_url() . 'index.php/exportar/cards/' . $chave . '?pg=' . $i . '">Página ' . $i . '</a></p>';
			}
			exit(0);
		}

		if (isset($_GET['pg'])) {
			$inicio = ($_GET['pg'] - 1) * $por_pagina;
			$this->db->limit($por_pagina, $inicio);
		}

		$data['cards'] = $this->db->select('F.*, S.status, FT.tipo as funil_tipo,
			FE.etapa as funil_etapa, C.nome as cliente, TDM.tipo as midia,
			I.referencia as imovel_angariado')
			->select('GROUP_CONCAT(U.nome SEPARATOR ", ") as responsaveis', false)
			->from('funil_leads F')
			->join('funil_leads_responsaveis FLR', 'FLR.funil_lead_id = F.id', 'left')
			->join('funil_tipos FT', 'FT.id = F.funil_tipo_id', 'left')
			->join('funil_etapas FE', 'FE.id = F.funil_etapa_id', 'left')
			->join('clientes C', 'F.cliente_id = C.id', 'left')
			->join('usuarios U', 'FLR.responsavel_id = U.id', 'left')
			->join('statusdeatendimento S', 'F.status_id = S.id', 'left')
			->join('tiposdemidia TDM', 'F.midia_id = TDM.id', 'left')
			->join('imoveis I', 'F.imovel_angariado = I.id', 'left')
			->where('F.imobiliaria_id', $imobiliaria['id'])
			->group_by('F.id')
			->order_by('F.id')
			->get()
			->result_array();

		foreach ($data['cards'] as $card) {
			$perfil = $this->atendimentos_model->get_perfil_atendimento($card['pedido_id']);
			$perfil['imobiliaria_id'] = $imobiliaria['id'];
			$perfil['parceiros'] = 1;
			$perfil['modo'] = 'simples';

			$oferecidos = $this->clientes_model->imoveis_oferecidos($perfil['cliente_id'], true);
			$perfil['not_ids'] = array_column($oferecidos, 'id');

			if (isset($perfil['cidade_id']) && $perfil['cidade_id'] == 0) unset($perfil['cidade_id']);

			if (isset($perfil['transacao']) && $perfil['transacao'] != '') {
				$perfil['tipo_' . $perfil['transacao']] = 1;
				unset($perfil['transacao']);
			} else {
				$perfil['tipo_venda'] = 1;
			}

			$pedido_vazio = true;
			$checar = array(
				'valor_inicial',
				'valor_final',
				'cidade_id',
				'dormitorio_inicial',
				'dormitorio_final',
				'tipo_id',
				'area_total_inicial',
				'area_total_final',
				'area_util_inicial',
				'area_util_final',
				'suites_inicial',
				'suites_final',
				'bairro_id'
			);

			foreach ($checar as $checar_item) {
				if (isset($perfil[$checar_item]) && $perfil[$checar_item]) {
					$pedido_vazio = false;
					break;
				}
			}

			if (isset($perfil['dormitorio_inicial']) && $perfil['dormitorio_inicial'] != 0) {
				$perfil['dormitorios_mais'] = $perfil['dormitorio_inicial'];
				unset($perfil['dormitorio_inicial']);
			}

			if (isset($perfil['dormitorio_final']) && $perfil['dormitorio_final'] != 0) {
				$perfil['dormitorios_menos'] = $perfil['dormitorio_final'];
				unset($perfil['dormitorio_final']);
			}

			if (!$pedido_vazio) {
				$imoveis = $this->imoveis_model->get_imoveis_api($perfil);

				foreach ($imoveis as $k => $v) {
					$imoveis[$k]['foto_principal'] = (substr($v['foto_principal'], 0, 4) == 'http') ? $v['foto_principal'] : ENDERECO_FOTOS . 'fotosimovel/' . $v['id'] . '/' . $v['foto_principal'];
				}
			} else {
				$imoveis = array();
			}

			$card['imoveis_no_perfil'] = $imoveis;
			$card['imoveis_oferecidos'] = $oferecidos;
		}

		$datetime = new DateTime();
		$data['data_hora'] = $datetime->format('Y-m-d H:i:s');

		$this->load->view('exportar/cards', $data);
	}
}
