<?php

class Clientes_model extends CI_Model
{

	function __construct()
	{
		$this->load->database();
		arrumar_timezone();
	}

	/* 
	 * Função para alterar o corretor de um cliente
	 * 
	 * Alterar o corretor do cliente e mover todos os pedidos e atendimentos para o novo corretor
	 * Muda a data de alteração do cliente para a data atual
	 * Grava log de alteração do cliente indicando o novo corretor
	 */
	public function alterar_corretor($cliente_id, $corretor_id)
	{
		// Buscar o corretor antigo
		$this->db->select('corretor_id')
			->from('clientes')
			->where('id', $cliente_id);
		$corretor_antigo_id = $this->db->get()->row()->corretor_id;

		// Alterar o corretor do cliente
		$this->db->set('corretor_id', $corretor_id)
			->where('id', $cliente_id)
			->update('clientes');

		// Buscar e alterar os leads do cliente
		$this->db->select('F.id, FLR.responsavel_id, U.nome as corretor')
			->from('funil_leads F')
			->join('funil_leads_responsaveis FLR', 'FLR.funil_lead_id = F.id')
			->join('usuarios U', 'U.id = FLR.responsavel_id')
			->where('F.cliente_id', $cliente_id);
		$leads = $this->db->get()->result_array();
		foreach ($leads as $lead) {
			if ($lead['responsavel_id'] != $corretor_id) {
				$this->db->set('responsavel_id', $corretor_id)
					->where('funil_lead_id', $lead['id'])
					->update('funil_leads_responsaveis');
			}
		}

		// Buscar e alteraros atendimentos do cliente
		$this->db->select('A.id, A.corretor_id')
			->from('atendimentos A')
			->where('A.cliente_id', $cliente_id);
		$atendimentos = $this->db->get()->result_array();
		foreach ($atendimentos as $atendimento) {
			if ($atendimento['corretor_id'] != $corretor_id) {
				$this->db->set('corretor_id', $corretor_id)
					->where('id', $atendimento['id'])
					->update('atendimentos');
			}
		}

		// Gravar log
		$log = array();
		$log['mensagem'] = 'Cliente Trocado de corretor';
		$tmp = $this->clientes_model->get_cliente($cliente_id);

		$cliente = array();
		foreach ($tmp[0] as $key => $value) {
			if (isset($value) && $value != '') $cliente[$key] = $value;
		}
		$log['cliente'] = $cliente;

		$this->load->model('usuarios_model', 'usuarios');
		$corretor_antigo = $this->usuarios->get_usuario($corretor_antigo_id);
		$corretor_novo = $this->usuarios->get_usuario($corretor_id);

		$log['cliente']['corretor_nome'] = $corretor_novo->nome;
		$log['cliente']['corretor_antigo'] = $corretor_antigo->nome;

		gravarLogCliente('Cliente Trocado de corretor', $corretor_id, $log);

		$this->set_alteracao($cliente_id);

		return true;
	}

	/* Função para deletar cliente
	 */
	public function delete($id)
	{
		if ($id != '') {
			$this->db->where('id', $id);
			return $this->db->delete('clientes');
		}
	}

	/* Função para buscar dados do cliente
	 */
	public function get_clientes($retorno = 'array', $busca)
	{
		if (!isset($busca['where']) || $busca['where'] == '') {
			//Busca padrão

			$inicio = (isset($busca['page'])) ? $busca['page'] : 0;
			if ($inicio > 0) {
				$inicio = ($inicio - 1) * $this->session->userdata('itens_por_pagina');
			}

			if (isset($busca['telefone3']) && $busca['telefone3'] != '') {
				$busca['telefone3'] = convert_phone_regex($busca['telefone3']);
				$this->db->where('(C.telefone1 regexp \'' . $busca['telefone3'] . '\' OR C.telefone2 regexp \'' . $busca['telefone3'] . '\' OR C.telefone3 regexp \'' . $busca['telefone3'] . '\' OR C.conjuge_telefone regexp \'' . $busca['telefone3'] . '\')');
			} else if (isset($busca['telefone2']) && $busca['telefone2'] != '') {
				$busca['telefone2'] = convert_phone_regex($busca['telefone2']);
				$this->db->where('(C.telefone1 regexp \'' . $busca['telefone2'] . '\' OR C.telefone2 regexp \'' . $busca['telefone2'] . '\' OR C.telefone3 regexp \'' . $busca['telefone2'] . '\' OR C.conjuge_telefone regexp \'' . $busca['telefone2'] . '\')');
			} else if (isset($busca['telefone']) && $busca['telefone'] != '') {
				$telefone = preg_replace('/[^0-9]/', '', $busca['telefone']);
				$ddi = '';
				$ddd = '';

				if (substr($telefone, 0, 2) == '55') {
					$ddi = substr($telefone, 0, 2);
					$telefone = substr($telefone, 2);
				}

				// Telefone já com ddd, separar
				if (strlen($telefone) > 9) {
					$ddd = substr($telefone, 0, 2);
					$telefone = substr($telefone, 2);
				}

				if (strlen($telefone) == 8) {
					$telefone2 = '9' . $telefone;
				} else if (strlen($telefone) == 9) {
					$telefone2 = substr($telefone, 1);
				} else {
					$telefone2 = '';
				}

				$where = "(
					REGEXP_REPLACE(C.telefone1,\"[^0-9]+\", \"\")  = '" . $telefone . "' OR 
					REGEXP_REPLACE(C.telefone2,\"[^0-9]+\", \"\") = '" . $telefone . "' OR 
					REGEXP_REPLACE(C.telefone3,\"[^0-9]+\", \"\") = '" . $telefone . "' OR 
					REGEXP_REPLACE(C.telefone1,\"[^0-9]+\", \"\") = '" . $telefone2 . "' OR 
					REGEXP_REPLACE(C.telefone2,\"[^0-9]+\", \"\") = '" . $telefone2 . "' OR 
					REGEXP_REPLACE(C.telefone3,\"[^0-9]+\", \"\") = '" . $telefone2 . "' OR
					C.telefone1 = '" . $ddi . $ddd . $telefone . "'
					) ";
				$this->db->where($where);
			}

			if (isset($busca['email']) && $busca['email'] != '') {
				$this->db->where('C.email', $busca['email']);
			}
			if (isset($busca['ddd']) && $busca['ddd'] != '') {
				$this->db->where(' (C.ddd1 = \'' . $busca['ddd'] . '\' OR C.ddd1 IS NULL OR C.ddd1=\'\') ', false, false);
			}
			if (isset($busca['id']) && $busca['id'] != '') {
				$this->db->where('C.id', $busca['id']);
			}
			if (isset($busca['codigo']) && $busca['codigo'] != '') {
				$this->db->where('C.codigo', $busca['codigo']);
			}
			if (isset($busca['nome']) && $busca['nome'] != '') {
				$this->db->like('C.nome', $busca['nome']);
			}
			if (isset($busca['rg']) && $busca['rg'] != '') {
				$this->db->where('C.rg', $busca['rg']);
			}
			if (isset($busca['cpf']) && $busca['cpf'] != '') {
				$this->db->where('C.cpf', $busca['cpf']);
			}
			if (isset($busca['corretor_id']) && $busca['corretor_id'] != '') {
				$this->db->where('C.corretor_id', $busca['corretor_id']);
			}
			if (isset($busca['imobiliaria_id']) && $busca['imobiliaria_id'] != '') {
				$this->db->where('U.imobiliaria_id', $busca['imobiliaria_id']);
			}

			if ($this->session->userdata('imobiliaria_id') == 1672) {
				$this->db->where('C.proprietario_id IS NULL', false, false);
			}

			if ($retorno == 'array' || $retorno == 'todos') {
				$this->db->select('C.id, C.nome, C.alteracao, C.email, U.Nome AS corretor, U.id AS corretor_id, C.gerente_id');
				$this->db->select('U.ativo, DATEDIFF(now(),C.alteracao) AS dias', FALSE);
				$this->db->select('C.telefone1, C.ddd1, C.ddd2, C.telefone2, C.categoria');
				$this->db->select('C.telefone3, C.ddd3, C.conjuge_telefone, C.conjuge_ddd');
				$this->db->select('C.codigo, U.telefone AS corretor_telefone, U.email AS corretor_email');
				$this->db->select('CASE WHEN (SELECT A.data FROM agendamento A WHERE A.cliente_id = C.id AND A.data>CURDATE() ORDER BY A.data DESC LIMIT 1 ) IS NULL THEN 0 ELSE 1 END as com_agendamento', false, false);
				$this->db->from('clientes C');
				$this->db->join('usuarios U', 'U.id = C.corretor_id');
				$this->db->where('U.imobiliaria_id', $this->session->userdata('imobiliaria_id'));
				$this->db->order_by('C.nome');
				if ($retorno == 'array') {
					$this->db->limit($this->session->userdata('itens_por_pagina'), $inicio);
				}
				$query = $this->db->get();

				return $query->result_array();
			} else {
				$this->db->from('clientes C');
				$this->db->join('usuarios U', 'U.id = C.corretor_id');
				$this->db->distinct();
				$query = $this->db->get();
				return $query->num_rows();
			}
		} else {
			// Busca avançada
			$inicio = $busca['page'];
			if ($inicio > 0) {
				$inicio = ($inicio - 1) * $this->session->userdata('itens_por_pagina');
			}
			$this->db->where($busca['where']);

			if ($this->session->userdata('imobiliaria_id') == 1672) {
				$this->db->where('C.proprietario_id IS NULL', false, false);
			}

			if ($retorno == 'array' || $retorno == 'todos') {
				$this->db->select('C.id, C.nome, C.alteracao, C.email, U.Nome AS corretor, U.ativo');
				$this->db->from('clientes C');
				$this->db->join('usuarios U', 'U.id = C.corretor_id');
				$this->db->order_by('C.nome');
				if ($retorno == 'array') {
					$this->db->limit($this->session->userdata('itens_por_pagina'), $inicio);
				}
				$query = $this->db->get();
				return $query->result_array();
			} else {
				$this->db->from('clientes C');
				$this->db->join('usuarios U', 'U.id = C.corretor_id');
				return $this->db->count_all_results();
			}
		}
	}

	public function get_clientes_atrasados_por_corretor()
	{
		$this->db->select('U.nome, U.id as corretor_id, Count(C.id) as total', false);
		$this->db->from('clientes C');
		$this->db->join('usuarios U', 'U.id = C.corretor_id');
		$this->db->where('DATE_SUB(CURDATE(),INTERVAL ' . $this->session->userdata('dias_atraso') . ' DAY) >= C.alteracao');
		$this->db->where('CASE WHEN (SELECT A.data FROM agendamento A WHERE A.cliente_id = C.id AND A.data>CURDATE() ORDER BY A.data DESC LIMIT 1 ) IS NULL THEN 0 ELSE 1 END < 1', false, false);
		$this->db->where('( DATE(C.procurar_ate) >= DATE(NOW()) OR C.procurar_ate IS NULL) ');
		if ($this->session->userdata('nivel') == 1) {
			$this->db->where(' (U.id = ' . $this->session->userdata('id') . ' OR U.imobiliaria_id=' . $this->session->userdata('imobiliaria_id') . ') ', false, false);
		} else if ($this->session->userdata('nivel') == 3) {
			$this->db->where(' (U.id = ' . $this->session->userdata('id') . ' OR U.gerente_id=' . $this->session->userdata('id') . ') ', false, false);
		} else {
			$this->db->where('U.id', $this->session->userdata('id'));
		}
		$this->db->where('U.nivel!=', 5, false);
		$this->db->order_by('C.nome');
		$this->db->group_by('U.id');
		$query = $this->db->get();
		return $query->result_array();
	}

	public function get_clientes_busca_global($query = null)
	{
		$this->db->select('C.id, C.nome, C.email, U.id AS corretor_id, U.Nome AS corretor');
		$this->db->select('U.apelido AS corretor_apelido');
		$this->db->select('C.ddd1, C.telefone1, C.ddd2, C.telefone2, C.ddd3');
		$this->db->select('C.telefone3, C.email, C.conjuge_ddd, C.conjuge_telefone');
		$this->db->select('DATE_FORMAT(C.inclusao,\'%d/%m/%Y\') AS cadastro', FALSE);
		$this->db->select('DATE_FORMAT(C.alteracao,\'%d/%m/%Y %H:%i\') AS alteracao', FALSE);
		$this->db->select('DATEDIFF(now(),C.alteracao) AS dias', FALSE);
		$this->db->select('U.ativo');
		$this->db->from('clientes C');
		$this->db->join('usuarios U', 'U.id = C.corretor_id');
		$this->db->where('C.desconsiderar <>', 1);

		if (isset($query) && $query != '') {
			$where = "";
			if (
				$this->session->userdata('imobiliaria_corretor_clientes')
				|| $this->session->userdata('nivel') == '1'
			) {
				$where = "";
			} else if ($this->session->userdata('imobiliaria_id') != 1672) {
				$where = " (C.corretor_id = " . $this->session->userdata('id') . " OR U.nivel = 5) AND ";
			}

			$telefone = preg_replace('/[^0-9]/', '', $query);
			$ddi = '';
			$ddd = '';

			if (substr($telefone, 0, 2) == '55') {
				$ddi = substr($telefone, 0, 2);
				$telefone = substr($telefone, 2);
			}

			// Telefone já com ddd, separar
			if (strlen($telefone) > 9) {
				$ddd = substr($telefone, 0, 2);
				$telefone = substr($telefone, 2);
			}

			if (strlen($telefone) == 8) {
				$telefone2 = '9' . $telefone;
			} else if (strlen($telefone) == 9) {
				$telefone2 = substr($telefone, 1);
			}

			// Só buscar o telefone quando estiver completo
			if (strlen($telefone) < 8) {
				$telefone = '';
				$telefone2 = '';
			}

			if ($telefone !== '' && $telefone2 !== '') {
				$busca_telefone = "
					REGEXP_REPLACE(C.telefone1,\"[^0-9]+\", \"\")  = '" . $telefone . "' OR 
					REGEXP_REPLACE(C.telefone2,\"[^0-9]+\", \"\") = '" . $telefone . "' OR 
					REGEXP_REPLACE(C.telefone3,\"[^0-9]+\", \"\") = '" . $telefone . "' OR 
					REGEXP_REPLACE(C.telefone1,\"[^0-9]+\", \"\") = '" . $telefone2 . "' OR 
					REGEXP_REPLACE(C.telefone2,\"[^0-9]+\", \"\") = '" . $telefone2 . "' OR 
					REGEXP_REPLACE(C.telefone3,\"[^0-9]+\", \"\") = '" . $telefone2 . "' OR 
					REGEXP_REPLACE(C.conjuge_telefone,\"[^0-9]+\", \"\") = '" . $telefone . "' OR 
					REGEXP_REPLACE(C.conjuge_telefone,\"[^0-9]+\", \"\") = '" . $telefone2 . "' OR
					C.telefone1 = '" . $ddi . $ddd . $telefone . "' OR 
					";
			} else {
				$busca_telefone = '';
			}

			$where .= "(
				C.nome = '" . $query . "' OR 
				{$busca_telefone}
				C.email = '" . $query . "') ";
			$this->db->where($where, false, false);
		} else {
			if (isset($_GET['id']) && $_GET['id'] != '') {
				$this->db->where('C.id', $_GET['id']);
			} else if (!$this->session->userdata('imobiliaria_corretor_clientes') || $this->session->userdata('nivel') == '1') {
				// $this->db->where('C.corretor_id', $this->session->userdata('id'));
			}
		}

		$this->db->where('U.imobiliaria_id', $this->session->userdata('imobiliaria_id'));
		$this->db->order_by('C.nome, C.proprietario_id');
		$query = $this->db->get();

		$clientes = $query->result_array();

		// Remover clientes duplicados para a axis (tem vários cadastrados 2 vezes, uma como cliente e outra como proprietário)
		$resposta = array();
		if ($this->session->userdata('imobiliaria_id') == 1672) {
			$cliente_anterior = null;
			foreach ($clientes as $key => $cliente) {
				if (
					$cliente_anterior['nome'] == $cliente['nome']
					&& $cliente_anterior['email'] == $cliente['email']
					&& $cliente_anterior['telefone1'] == $cliente['telefone1']
				) {
					// Não adicionar
				} else {

					$cliente['mostrar'] = 1;
					if (
						$this->session->userdata('nivel') != 1
						&& $cliente['corretor_id'] != $this->session->userdata('id')
					) {
						$cliente['mostrar'] = 0;
					}

					array_push($resposta, $cliente);
				}
				$cliente_anterior = $cliente;
			}
			$clientes = $resposta;
		}

		return $clientes;
	}

	/* Dados completos dos clientes, usado para exportação */
	public function get_clientes_completo($imobiliaria_id)
	{
		$this->load->model('atendimentos_model');
		$this->load->model('emailsmarketing_model');

		$this->session->userdata('imobiliaria_id', $imobiliaria_id);
		$por_pagina = 1000;

		if ($imobiliaria_id == 1672) {
			$this->db->where('C.proprietario_id IS NULL', false, false);

			if (isset($_GET['pg'])) {
				$inicio = ($_GET['pg'] - 1) * $por_pagina;
				$this->db->limit($por_pagina, $inicio);
			}
		}

		$clientes = array();
		$result = $this->db->select('C.*, M.tipo AS tipo_de_midia, 
			U.apelido, U.nome as corretor')
			->from('clientes C')
			->join('usuarios U', 'U.id = C.corretor_id')
			->join('tiposdemidia M', 'M.id = C.tipo_de_midia_id', 'left')
			->where('C.imobiliaria_id', $imobiliaria_id)
			->get()
			->result_array();
		foreach ($result as $cliente) {
			$clientes[$cliente['id']] = $cliente;
		}

		// Initialize arrays for related data
		foreach ($clientes as $cliente_id => $cliente) {
			$clientes[$cliente_id]['atendimentos'] = array();
			$clientes[$cliente_id]['agendamentos'] = array();
			$clientes[$cliente_id]['recontatos'] = array();
		}

		$atendimentos = $this->atendimentos_model->get_atendimentos();
		foreach ($atendimentos as $atendimento) {
			if (!isset($clientes[$atendimento['cliente_id']])) {
				continue;
			}
			$clientes[$atendimento['cliente_id']]['atendimentos'][] = $atendimento;
		}

		$agendamentos = $this->db->query('SELECT A.id, A.data, A.texto, A.hora, U.nome,
			A.inclusao, FL.sequencial as funil_sequencial, U2.nome as responsavel,
			A.status, A.cliente_id
			FROM agendamento A
			JOIN usuarios U ON A.corretor_id=U.id
			LEFT JOIN usuarios U2 ON A.responsavel_id=U2.id
			LEFT JOIN funil_leads FL ON A.funil_id=FL.id
			WHERE U.imobiliaria_id = ' . $imobiliaria_id . '
			ORDER BY A.data DESC, A.hora DESC')
			->result_array();
		foreach ($agendamentos as $agendamento) {
			if (!isset($clientes[$agendamento['cliente_id']])) {
				continue;
			}
			$clientes[$agendamento['cliente_id']]['agendamentos'][] = $agendamento;
		}

		$recontatos = $this->db->query('SELECT R.data, R.texto, U.nome,
			R.cliente_id, FL.sequencial as funil_sequencial
			FROM recontato R
			JOIN usuarios U ON R.corretor_id=U.id
			LEFT JOIN funil_leads FL ON R.funil_id=FL.id
			WHERE U.imobiliaria_id = ' . $imobiliaria_id . '
			ORDER BY R.data DESC')
			->result_array();
		foreach ($recontatos as $recontato) {
			if (!isset($clientes[$recontato['cliente_id']])) {
				continue;
			}
			$clientes[$recontato['cliente_id']]['recontatos'][] = $recontato;
		}

		return $clientes;
	}

	public function get_clientes_select($usuario_id)
	{
		$this->db->select('C.id, C.nome, C.ddd1, C.telefone1');
		$this->db->select('C.email, C.alteracao');
		$this->db->from('clientes C');
		$this->db->join('usuarios U', 'U.id=C.corretor_id');
		$this->db->where('C.corretor_id', $usuario_id);
		$this->db->order_by('C.nome');
		$result = $this->db->get();
		$clientes = array();
		if ($result->num_rows() > 0) {
			foreach ($result->result_array() as $row) {
				$clientes[$row['id']] = $row['nome'] . ' - ' . $row['telefone1'] . ' - ' . $row['email'] . ' - ' . date("d/m/Y", strtotime($row['alteracao']));
			}
		}
		return $clientes;
	}

	/* Função para buscar dados dos clientes da imobiliaria */
	public function get_clientes2($buscar, $pagina = null, $order = null)
	{
		$corretor_id = $this->session->userdata('id');
		$this->db->select('C.*, U.apelido, U.nome as corretor')
			->select('DATEDIFF(now(),C.alteracao) AS dias', FALSE)
			->select('num_recontatos(C.id) AS recontatos', FALSE)
			->select('num_atendimentos(C.id) AS pedidos', FALSE)
			->select('num_oferecidos(C.id) AS oferecidos', FALSE)
			->select('CASE WHEN (SELECT A.data FROM agendamento A WHERE A.cliente_id = C.id AND A.data>CURDATE() ORDER BY A.data DESC LIMIT 1 ) IS NULL THEN 0 ELSE 1 END as com_agendamento', false, false)
			->from('clientes C')
			->join('usuarios U', 'U.id = C.corretor_id');

		if ($this->session->userdata('imobiliaria_id') == 1672) {
			$this->db->where('C.proprietario_id IS NULL', false, false);
		}

		if (isset($pagina) && $pagina == 'total') {
		} else {
			$inicio = $pagina;
			if (isset($inicio) && $inicio > 0) {
				$inicio = ($inicio - 1) * $this->session->userdata('itens_por_pagina');
				$this->db->limit($this->session->userdata('itens_por_pagina'), $inicio);
			}
		}

		if (isset($buscar)) {
			$mostrar_todos = true;
			$desconsiderar = false;

			foreach ($buscar as $key => $value) {
				if ($key == 'nome' && $value != '') {
					$this->db->like('C.nome', $value);
				} else if ($key == 'email' && $value != '') {
					$this->db->like('C.email', $value);
				} else if ($key == 'conjuge_nome' && $value != '') {
					$this->db->like('C.conjuge_nome', $value);
				} else if ($key == 'cpf' && $value != '') {
					$this->db->like('C.cpf', $value);
				} else if ($key == 'telefone' && $value != '') {
					$telefone = preg_replace('/[^0-9]/', '', $value);
					$ddi = '';
					$ddd = '';

					if (substr($telefone, 0, 2) == '55') {
						$ddi = substr($telefone, 0, 2);
						$telefone = substr($telefone, 2);
					}

					// Telefone já com ddd, separar
					if (strlen($telefone) > 9) {
						$ddd = substr($telefone, 0, 2);
						$telefone = substr($telefone, 2);
					}

					if (strlen($telefone) == 8) {
						$telefone2 = '9' . $telefone;
					} else if (strlen($telefone) == 9) {
						$telefone2 = substr($telefone, 1);
					} else {
						$telefone2 = 'aaaa';
					}

					$where = "(
						REGEXP_REPLACE(C.telefone1,\"[^0-9]+\", \"\") LIKE '%" . $telefone . "%' OR 
						REGEXP_REPLACE(C.telefone2,\"[^0-9]+\", \"\") LIKE '%" . $telefone . "%' OR 
						REGEXP_REPLACE(C.telefone3,\"[^0-9]+\", \"\") LIKE '%" . $telefone . "%' OR 
						REGEXP_REPLACE(C.telefone1,\"[^0-9]+\", \"\") LIKE '%" . $telefone2 . "%' OR 
						REGEXP_REPLACE(C.telefone2,\"[^0-9]+\", \"\") LIKE '%" . $telefone2 . "%' OR 
						REGEXP_REPLACE(C.telefone3,\"[^0-9]+\", \"\") LIKE '%" . $telefone2 . "%' OR
						C.telefone1 = '" . $ddi . $ddd . $telefone . "'
						) ";
					$this->db->where($where);
				} else if (($key == 'renda_pessoal' || $key == 'renda_familia' || $key == 'fgts' || $key == 'sinal') && $value != '') {
					$this->db->where('C.' . $key, tratar_valor_para_bd($value, 'float'));
				} else if (($key == 'valor_de' || $key == 'area_de' || $key == 'dormitorios_de' || $key == 'suite_de' || $key == 'garagem_de') && $value != '') {
					if ($buscar[str_replace('de', 'ate', $key)] != '') {
						$this->db->where('( (C.' . $key . '<=' . tratar_valor_para_bd($value, 'float') . ' AND ' . str_replace('de', 'ate', $key) . ' >= ' . tratar_valor_para_bd($value, 'float') . ' ) OR (C.' . $key . '<=' . tratar_valor_para_bd($buscar[str_replace('de', 'ate', $key)], 'float') . ' AND ' . str_replace('de', 'ate', $key) . ' >= ' . tratar_valor_para_bd($buscar[str_replace('de', 'ate', $key)], 'float') . ' ) )', FALSE, FALSE);
					} else {
						$this->db->where('C.' . $key . '>=', tratar_valor_para_bd($value, 'float'), FALSE);
					}
				} else if (($key == 'valor_ate' || $key == 'area_ate' || $key == 'dormitorios_ate' || $key == 'suite_ate' || $key == 'garagem_ate') && $value != '') {
					if ($buscar[str_replace('ate', 'de', $key)] != '') {
					} else {
						$this->db->where('C.' . $key . '<=', tratar_valor_para_bd($value, 'float'), FALSE);
					}
				} elseif ($key == 'uso' && isset($value)) {
					$this->db->join('perfil_cliente_uso PU', 'PU.cliente_id = C.id', 'left');
					$this->db->where_in('PU.uso', $value);
				} elseif ($key == 'pagamento' && isset($value)) {
					$this->db->join('perfil_cliente_pagamento PP', 'PP.cliente_id = C.id', 'left');
					$this->db->where_in('PP.pagamento', $value);
				} elseif ($key == 'vocacao' && isset($value)) {
					$this->db->join('perfil_cliente_vocacao PV', 'PV.cliente_id = C.id', 'left');
					$this->db->where_in('PV.vocacao', $value);
				} elseif ($key == 'tipo' && isset($value)) {
					$this->db->join('perfil_cliente_tipo PT', 'PT.cliente_id = C.id', 'left');
					$this->db->where_in('PT.tipo_id', $value);
				} elseif ($key == 'bairro' && isset($value)) {
					$this->db->join('perfil_cliente_bairro PB', 'PB.cliente_id = C.id', 'left');
					$this->db->where_in('PB.bairro_id', $value);
				} else if ($key == 'data_de' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.alteracao >=', $mysqldate);
				} else if ($key == 'data_ate' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.alteracao <=', $mysqldate);
				} else if ($key == 'data_de_cadastro' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.inclusao >=', $mysqldate);
				} else if ($key == 'data_ate_cadastro' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.inclusao <=', $mysqldate);
				} else if ($key == 'renda_pessoal_inicial' && $value != '') {
					$this->db->where('C.renda_pessoal>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_pessoal_final' && $value != '') {
					$this->db->where('C.renda_pessoal<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_familia_inicial' && $value != '') {
					$this->db->where('C.renda_familia>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_familia_final' && $value != '') {
					$this->db->where('C.renda_familia<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'fgts_inicial' && $value != '') {
					$this->db->where('C.fgts>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'fgts_final' && $value != '') {
					$this->db->where('C.fgts<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'sinal_inicial' && $value != '') {
					$this->db->where('C.sinal>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'sinal_final' && $value != '') {
					$this->db->where('C.sinal<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'procurando_desde_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.procurando_desde >=', $mysqldate);
				} else if ($key == 'procurando_desde_final' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.procurando_desde <=', $mysqldate);
				} else if ($key == 'aniversario_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate1 = '2014-' . $aux[1] . "-" . $aux[0];

					if ($buscar['aniversario_final'] != '') {
						$aux = explode("/", $buscar['aniversario_final']);
						$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];
					} else {
						$mysqldate2 = '2014-12-31';
					}

					$this->db->where('date_format(C.nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} else if ($key == 'aniversario_final' && $value != '' && $buscar['aniversario_inicial'] == '') {
					$mysqldate1 = '2014-01-01';

					$aux = explode("/", $buscar['aniversario_final']);
					$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];

					$this->db->where('date_format(C.nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} else if ($key == 'aniversario_conjuge_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate1 = '2014-' . $aux[1] . "-" . $aux[0];

					if ($buscar['aniversario_conjuge_final'] != '') {
						$aux = explode("/", $buscar['aniversario_conjuge_final']);
						$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];
					} else {
						$mysqldate2 = '2014-12-31';
					}

					$this->db->where('date_format(C.conjuge_nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} else if ($key == 'aniversario_conjuge_final' && $value != ''  && $buscar['aniversario_conjuge_inicial'] == '') {
					$mysqldate1 = '2014-01-01';

					$aux = explode("/", $buscar['aniversario_conjuge_final']);
					$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];

					$this->db->where('date_format(C.conjuge_nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} elseif ($value != '' && $key == 'dias_inicial') {
					$this->db->where(' (DATEDIFF(now(),C.alteracao) >= ' . $value . ') ', false, false);
					$this->db->where('CASE WHEN (SELECT A.data FROM agendamento A WHERE A.cliente_id = C.id AND A.data>CURDATE() ORDER BY A.data DESC LIMIT 1 ) IS NULL THEN 0 ELSE 1 END < 1', false, false);
				} elseif ($value != '' && $key == 'dias_final') {
					$this->db->where(' (DATEDIFF(now(),C.alteracao) <= ' . $value . ') ', false, false);
				} elseif ($key == 'oferta_ativa' && $value == '1') {
					$this->db->where('oferta_ativa', 1);
				} elseif ($key == 'oferta_ativa' && $value == '0') {
					$this->db->where(' (C.oferta_ativa=0 OR C.oferta_ativa IS NULL OR C.oferta_ativa=\'\')', false, false);
				} elseif ($key == 'ativo' && $value == '1') {
					$this->db->where(' (C.desconsiderar=0 OR C.desconsiderar IS NULL OR C.desconsiderar=\'\')', false, false);
				} elseif ($key == 'ativo' && $value == '0') {
					$this->db->where('desconsiderar', 1);
					$desconsiderar = true;
					$mostrar_todos = true;
				} elseif ($value != '' && $key != 'aniversario_final' && $key != 'aniversario_conjuge_final') {
					$this->db->where('C.' . $key, $value);
				}
			}

			if (!$desconsiderar) {
				$this->db->where(' (C.desconsiderar=0 OR C.desconsiderar IS NULL OR C.desconsiderar=\'\')', false, false);
			}

			$this->db->where(' ( DATE(C.procurar_ate) >= DATE(NOW()) OR C.procurar_ate IS NULL ) ', false, false);

			if (($this->session->userdata('nivel') != '1' && $this->session->userdata('nivel') != '3')) {
				$this->db->where('corretor_id', $corretor_id);
			} else if (isset($buscar['corretor_id']) && $buscar['corretor_id'] != '') {
				$this->db->where('C.corretor_id', $buscar['corretor_id']);
			}

			if ($this->session->userdata('nivel') == '3') {
				$this->db->where('(U.gerente_id = ' . $this->session->userdata('id') . ' OR U.id=' . $this->session->userdata('id') . ')');
			}

			$this->db->where('U.imobiliaria_id', $this->session->userdata('imobiliaria_id'));
		}

		if (isset($pagina) && $pagina == 'total') {
			return $this->db->count_all_results();
		} else {
			$this->db->order_by($this->session->userdata('order_cliente'));
			$query = $this->db->get();
			return $query->result_array();
		}
	}

	/* Função para buscar dados dos clientes da imobiliaria */
	public function get_clientes_contatos($buscar, $pagina = null, $order = null)
	{

		$corretor_id = $this->session->userdata('id');
		$this->db->select('C.id, U.nome as corretor, C.nome, C.inclusao, C.ddd1, C.telefone1, C.imovel_origem_id, C.imovel_atual_id')
			->select('C.email, C.categoria, COUNT(DISTINCT HV.imovel_id) as numero_visitas, C.texto', FALSE)
			->select('COUNT(DISTINCT R.id) as numero_recontatos', FALSE)
			->select('recontatos_json(C.id) as recontatos', FALSE)
			->select('DATEDIFF(now(),C.alteracao) AS dias', FALSE)
			->from('clientes C')
			->join('usuarios U', 'U.id = C.corretor_id')
			->join('historicovisita HV', 'HV.cliente_id = C.id', 'left')
			->join('recontato R', 'R.cliente_id = C.id', 'left')
			->group_by('C.id');

		if (isset($buscar)) {
			$mostrar_todos = true;
			$desconsiderar = false;

			foreach ($buscar as $key => $value) {
				if ($key == 'nome' && $value != '') {
					$this->db->like('C.nome', $value);
				} else if ($key == 'email' && $value != '') {
					$this->db->like('C.email', $value);
				} else if ($key == 'conjuge_nome' && $value != '') {
					$this->db->like('C.conjuge_nome', $value);
				} else if ($key == 'cpf' && $value != '') {
					$this->db->like('C.cpf', $value);
				} else if ($key == 'telefone' && $value != '') {
					$value = convert_phone_regex($value);
					$this->db->where('(C.telefone1 regexp \'' . $value . '\' OR C.telefone2 regexp \'' . $value . '\' OR C.telefone3 regexp \'' . $value . '\' OR C.conjuge_telefone regexp \'' . $value . '\')');
				} else if (($key == 'renda_pessoal' || $key == 'renda_familia' || $key == 'fgts' || $key == 'sinal') && $value != '') {
					$this->db->where('C.' . $key, tratar_valor_para_bd($value, 'float'));
				} else if (($key == 'valor_de' || $key == 'area_de' || $key == 'dormitorios_de' || $key == 'suite_de' || $key == 'garagem_de') && $value != '') {
					if ($buscar[str_replace('de', 'ate', $key)] != '') {
						$this->db->where('( (C.' . $key . '<=' . tratar_valor_para_bd($value, 'float') . ' AND ' . str_replace('de', 'ate', $key) . ' >= ' . tratar_valor_para_bd($value, 'float') . ' ) OR (C.' . $key . '<=' . tratar_valor_para_bd($buscar[str_replace('de', 'ate', $key)], 'float') . ' AND ' . str_replace('de', 'ate', $key) . ' >= ' . tratar_valor_para_bd($buscar[str_replace('de', 'ate', $key)], 'float') . ' ) )', FALSE, FALSE);
					} else {
						$this->db->where('C.' . $key . '>=', tratar_valor_para_bd($value, 'float'), FALSE);
					}
				} else if (($key == 'valor_ate' || $key == 'area_ate' || $key == 'dormitorios_ate' || $key == 'suite_ate' || $key == 'garagem_ate') && $value != '') {
					if ($buscar[str_replace('ate', 'de', $key)] != '') {
					} else {
						$this->db->where('C.' . $key . '<=', tratar_valor_para_bd($value, 'float'), FALSE);
					}
				} elseif ($key == 'uso' && isset($value)) {
					$this->db->join('perfil_cliente_uso PU', 'PU.cliente_id = C.id', 'left');
					$this->db->where_in('PU.uso', $value);
				} elseif ($key == 'pagamento' && isset($value)) {
					$this->db->join('perfil_cliente_pagamento PP', 'PP.cliente_id = C.id', 'left');
					$this->db->where_in('PP.pagamento', $value);
				} elseif ($key == 'vocacao' && isset($value)) {
					$this->db->join('perfil_cliente_vocacao PV', 'PV.cliente_id = C.id', 'left');
					$this->db->where_in('PV.vocacao', $value);
				} elseif ($key == 'tipo' && isset($value)) {
					$this->db->join('perfil_cliente_tipo PT', 'PT.cliente_id = C.id', 'left');
					$this->db->where_in('PT.tipo_id', $value);
				} elseif ($key == 'bairro' && isset($value)) {
					$this->db->join('perfil_cliente_bairro PB', 'PB.cliente_id = C.id', 'left');
					$this->db->where_in('PB.bairro_id', $value);
				} else if ($key == 'data_de' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.alteracao >=', $mysqldate);
				} else if ($key == 'data_ate' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.alteracao <=', $mysqldate);
				} else if ($key == 'data_de_cadastro' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.inclusao >=', $mysqldate);
				} else if ($key == 'data_ate_cadastro' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.inclusao <=', $mysqldate);
				} else if ($key == 'renda_pessoal_inicial' && $value != '') {
					$this->db->where('C.renda_pessoal>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_pessoal_final' && $value != '') {
					$this->db->where('C.renda_pessoal<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_familia_inicial' && $value != '') {
					$this->db->where('C.renda_familia>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_familia_final' && $value != '') {
					$this->db->where('C.renda_familia<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'fgts_inicial' && $value != '') {
					$this->db->where('C.fgts>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'fgts_final' && $value != '') {
					$this->db->where('C.fgts<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'sinal_inicial' && $value != '') {
					$this->db->where('C.sinal>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'sinal_final' && $value != '') {
					$this->db->where('C.sinal<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'procurando_desde_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.procurando_desde >=', $mysqldate);
				} else if ($key == 'procurando_desde_final' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.procurando_desde <=', $mysqldate);
				} else if ($key == 'aniversario_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate1 = '2014-' . $aux[1] . "-" . $aux[0];

					if ($buscar['aniversario_final'] != '') {
						$aux = explode("/", $buscar['aniversario_final']);
						$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];
					} else {
						$mysqldate2 = '2014-12-31';
					}

					$this->db->where('date_format(C.nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} else if ($key == 'aniversario_final' && $value != '' && $buscar['aniversario_inicial'] == '') {
					$mysqldate1 = '2014-01-01';

					$aux = explode("/", $buscar['aniversario_final']);
					$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];

					$this->db->where('date_format(C.nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} else if ($key == 'aniversario_conjuge_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate1 = '2014-' . $aux[1] . "-" . $aux[0];

					if ($buscar['aniversario_conjuge_final'] != '') {
						$aux = explode("/", $buscar['aniversario_conjuge_final']);
						$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];
					} else {
						$mysqldate2 = '2014-12-31';
					}

					$this->db->where('date_format(C.conjuge_nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} else if ($key == 'aniversario_conjuge_final' && $value != ''  && $buscar['aniversario_conjuge_inicial'] == '') {
					$mysqldate1 = '2014-01-01';

					$aux = explode("/", $buscar['aniversario_conjuge_final']);
					$mysqldate2 = '2014-' . $aux[1] . "-" . $aux[0];

					$this->db->where('date_format(C.conjuge_nascimento, \'%m-%d\') BETWEEN date_format( \'' . $mysqldate1 . '\', \'%m-%d\' ) AND date_format( \'' . $mysqldate2 . '\', \'%m-%d\' ) ', FALSE, FALSE);
				} elseif ($key == 'oferta_ativa' && $value == '1') {
					$this->db->where('oferta_ativa', 1);
				} elseif ($key == 'oferta_ativa' && $value == '0') {
					$this->db->where(' (C.oferta_ativa=0 OR C.oferta_ativa IS NULL OR C.oferta_ativa=\'\')', false, false);
				} elseif ($key == 'ativo' && $value == '1') {
					$this->db->where(' (C.desconsiderar=0 OR C.desconsiderar IS NULL OR C.desconsiderar=\'\')', false, false);
				} elseif ($key == 'ativo' && $value == '0') {
					$this->db->where('desconsiderar', 1);
					$desconsiderar = true;
					$mostrar_todos = true;
				} elseif ($value != '' && $key == 'dias_inicial') {
					$this->db->where(' (DATEDIFF(now(),C.alteracao) >= ' . $value . ') ', false, false);
					$this->db->where('CASE WHEN (SELECT A.data FROM agendamento A WHERE A.cliente_id = C.id AND A.data>CURDATE() ORDER BY A.data DESC LIMIT 1 ) IS NULL THEN 0 ELSE 1 END < 1', false, false);
				} elseif ($value != '' && $key == 'dias_final') {
					$this->db->where(' (DATEDIFF(now(),C.alteracao) <= ' . $value . ') ', false, false);
				} elseif ($value != '' && $key != 'aniversario_final' && $key != 'aniversario_conjuge_final') {
					$this->db->where('C.' . $key, $value);
				}
			}

			if (!$desconsiderar) {
				$this->db->where(' (C.desconsiderar=0 OR C.desconsiderar IS NULL OR C.desconsiderar=\'\')', false, false);
			}

			$this->db->where(' ( DATE(C.procurar_ate) >= DATE(NOW()) OR C.procurar_ate IS NULL) ', false, false);

			if (($this->session->userdata('nivel') != '1' && $this->session->userdata('nivel') != '3')) {
				$this->db->where('C.corretor_id', $corretor_id);
			} else if (isset($buscar['corretor_id']) && $buscar['corretor_id'] != '') {
				$this->db->where('C.corretor_id', $buscar['corretor_id']);
			}

			if ($this->session->userdata('nivel') == '3') {
				$this->db->where('(U.gerente_id = ' . $this->session->userdata('id') . ' OR U.id=' . $this->session->userdata('id') . ')');
			}

			$this->db->where('U.imobiliaria_id', $this->session->userdata('imobiliaria_id'));
		}

		$this->db->order_by($this->session->userdata('order_cliente'));

		$query = $this->db->get();

		return $query->result_array();
	}

	/* Função para buscar dados dos clientes da imobiliaria */
	public function get_clientes2_csv($buscar)
	{
		$corretor_id = $this->session->userdata('id');
		$this->db->select('C.email, C.nome AS cliente, C.ddd1, C.telefone1, U.nome AS corretor');
		$this->db->from('clientes C');
		$this->db->join('usuarios U', 'U.id = C.corretor_id');

		if (isset($buscar)) {

			foreach ($buscar as $key => $value) {
				if ($key == 'nome' && $value != '') {
					$this->db->like('C.nome', $value);
				} else if ($key == 'email' && $value != '') {
					$this->db->like('C.email', $value);
				} else if ($key == 'conjuge_nome' && $value != '') {
					$this->db->like('C.conjuge_nome', $value);
				} else if ($key == 'cpf' && $value != '') {
					$this->db->like('C.cpf', $value);
				} else if ($key == 'telefone' && $value != '') {
					$value = convert_phone_regex($value);
					$this->db->where('(C.telefone1 regexp \'' . $value . '\' OR C.telefone2 regexp \'' . $value . '\' OR C.telefone3 regexp \'' . $value . '\' OR C.conjuge_telefone regexp \'' . $value . '\')');
				} else if (($key == 'renda_pessoal' || $key == 'renda_familia' || $key == 'fgts' || $key == 'sinal') && $value != '') {
					$this->db->where('C.' . $key, tratar_valor_para_bd($value, 'float'));
				} else if (($key == 'valor_de' || $key == 'area_de' || $key == 'dormitorios_de' || $key == 'suite_de' || $key == 'garagem_de') && $value != '') {
					$this->db->where('C.' . $key . '>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if (($key == 'valor_ate' || $key == 'area_ate' || $key == 'dormitorios_ate' || $key == 'suite_ate' || $key == 'garagem_ate') && $value != '') {
					$this->db->where('C.' . $key . '<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} elseif ($key == 'uso' && isset($value)) {
					$this->db->join('perfil_cliente_uso PU', 'PU.cliente_id = C.id', 'left');
					$this->db->where_in('PU.uso', $value);
				} elseif ($key == 'pagamento' && isset($value)) {
					$this->db->join('perfil_cliente_pagamento PP', 'PP.cliente_id = C.id', 'left');
					$this->db->where_in('PP.pagamento', $value);
				} elseif ($key == 'vocacao' && isset($value)) {
					$this->db->join('perfil_cliente_vocacao PV', 'PV.cliente_id = C.id', 'left');
					$this->db->where_in('PV.vocacao', $value);
				} elseif ($key == 'tipo' && isset($value)) {
					$this->db->join('perfil_cliente_tipo PT', 'PT.cliente_id = C.id', 'left');
					$this->db->where_in('PT.tipo_id', $value);
				} elseif ($key == 'bairro' && isset($value)) {
					$this->db->join('perfil_cliente_bairro PB', 'PB.cliente_id = C.id', 'left');
					$this->db->where_in('PB.bairro_id', $value);
				} else if ($key == 'data_de' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.alteracao >=', $mysqldate);
				} else if ($key == 'data_ate' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.alteracao <=', $mysqldate);
				} else if ($key == 'renda_pessoal_inicial' && $value != '') {
					$this->db->where('C.renda_pessoal>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_pessoal_final' && $value != '') {
					$this->db->where('C.renda_pessoal<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_familia_inicial' && $value != '') {
					$this->db->where('C.renda_familia>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'renda_familia_final' && $value != '') {
					$this->db->where('C.renda_familia<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'fgts_inicial' && $value != '') {
					$this->db->where('C.fgts>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'fgts_final' && $value != '') {
					$this->db->where('C.fgts<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'sinal_inicial' && $value != '') {
					$this->db->where('C.sinal>=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'sinal_final' && $value != '') {
					$this->db->where('C.sinal<=', tratar_valor_para_bd($value, 'float'), FALSE);
				} else if ($key == 'procurando_desde_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.procurando_desde >=', $mysqldate);
				} else if ($key == 'procurando_desde_final' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.procurando_desde <=', $mysqldate);
				} else if ($key == 'aniversario_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.nascimento >=', $mysqldate);
				} else if ($key == 'aniversario_final' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.nascimento <=', $mysqldate);
				} else if ($key == 'aniversario_conjuge_inicial' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 00:00';
					$this->db->where('C.conjuge_nascimento >=', $mysqldate);
				} else if ($key == 'aniversario_conjuge_final' && $value != '') {
					$aux = explode("/", $value);
					$mysqldate = $aux[2] . "-" . $aux[1] . "-" . $aux[0] . ' 23:59';
					$this->db->where('C.conjuge_nascimento <=', $mysqldate);
				} elseif ($value != '' && $key == 'dias_inicial') {
					$this->db->where(' (DATEDIFF(now(),C.alteracao) >= ' . $value . ') ', false, false);
					$this->db->where('CASE WHEN (SELECT A.data FROM agendamento A WHERE A.cliente_id = C.id AND A.data>CURDATE() ORDER BY A.data DESC LIMIT 1 ) IS NULL THEN 0 ELSE 1 END < 1', false, false);
				} elseif ($value != '' && $key == 'dias_final') {
					$this->db->where(' (DATEDIFF(now(),C.alteracao) <= ' . $value . ') ', false, false);
				} elseif ($value != '') {
					$this->db->where('C.' . $key, $value);
				}
			}

			if (($this->session->userdata('nivel') != '1' && $this->session->userdata('nivel') != '3')) {
				$this->db->where('corretor_id', $corretor_id);
			} else if (isset($buscar['corretor_id']) && $buscar['corretor_id'] != '') {
				$this->db->where('C.corretor_id', $buscar['corretor_id']);
			}

			if ($this->session->userdata('nivel') == '3') {
				$this->db->where('(U.gerente_id = ' . $this->session->userdata('id') . ' OR U.id=' . $this->session->userdata('id') . ')');
			}

			$this->db->where('U.imobiliaria_id', $this->session->userdata('imobiliaria_id'));
		}
		$this->load->dbutil();
		$query = $this->db->get();
		//echo $this->db->last_query();
		return $this->dbutil->csv_from_result($query);
	}

	public function get_cliente($id)
	{
		$this->db->select('C.*')
			->select('U.ativo, DATEDIFF(now(),C.alteracao) AS dias', FALSE)
			->select('U.nome as corretor_nome')
			->from('clientes C')
			->join('usuarios U', 'U.id = C.corretor_id')
			->where('C.id', $id);
		$query = $this->db->get();
		$result = $query->result_array();

		foreach ($result as $key => $row) {
			$this->db->select('uso')
				->from('perfil_cliente_uso')
				->where('cliente_id', $id);
			$query = $this->db->get();
			$row['uso'] = $query->result_array();
			$result[$key] = $row;

			$this->db->select('pagamento')
				->from('perfil_cliente_pagamento')
				->where('cliente_id', $id);
			$query = $this->db->get();
			$row['pagamento'] = $query->result_array();
			$result[$key] = $row;

			$this->db->select('vocacao')
				->from('perfil_cliente_vocacao')
				->where('cliente_id', $id);
			$query = $this->db->get();
			$row['vocacao'] = $query->result_array();
			$result[$key] = $row;

			$this->db->select('T.tipo')
				->from('perfil_cliente_tipo PCT')
				->join('tipos T', 'PCT.tipo_id = T.id')
				->where('PCT.cliente_id', $id);
			$query = $this->db->get();
			$row['tipo'] = $query->result_array();
			$result[$key] = $row;

			$this->db->select('B.bairro')
				->from('perfil_cliente_bairro PCB')
				->join('bairros B', 'PCB.bairro_id = B.id')
				->where('PCB.cliente_id', $id);
			$query = $this->db->get();
			$row['bairros'] = $query->result_array();
			$result[$key] = $row;
		}

		return $result;
	}

	public function get_cliente_oferta_ativa($id_corretor)
	{
		$this->load->model('listasofertaativa_model');
		$listas = $this->listasofertaativa_model->get_listas();
		$listas_string = '';
		$separador = '';
		foreach ($listas as $lista) {
			$listas_string .= $separador . $lista['id'];
			$separador = ',';
		}

		$this->db->_protect_identifiers = FALSE;
		$this->db->select('C.*');
		$this->db->from('clientes C');
		$this->db->join('usuarios U', 'U.id = C.corretor_id');
		$this->db->where('C.desconsiderar != 1 AND C.oferta_ativa = 1 AND C.corretor_id = ' . $id_corretor, false, false);
		$this->db->or_where('U.imobiliaria_id = ' . $this->session->userdata('imobiliaria_id') . ' AND U.nivel=5', false, false);
		$this->db->order_by('ISNULL(lista_oferta_ativa), ' . (($listas_string) ? 'FIELD(lista_oferta_ativa ,' . $listas_string . '), ' : '') . ' RAND()', false, false);
		$query = $this->db->get();

		if (sizeof($query->result_array()) < 1) {
			$this->db->_protect_identifiers = FALSE;
			$this->db->select('C.*');
			$this->db->from('clientes C');
			$this->db->join('usuarios U', 'U.id = C.corretor_id');
			$this->db->where('C.desconsiderar !=', 1);
			$this->db->where('C.oferta_ativa', 1);
			$this->db->where('U.nivel', 5);
			$this->db->where('U.imobiliaria_id', $this->session->userdata('imobiliaria_id'));
			$this->db->where('C.lista_oferta_ativa IN (\'' . $listas_string . '\')', false, false);
			$this->db->order_by('ISNULL(lista_oferta_ativa), ' . (($listas_string) ? 'FIELD(lista_oferta_ativa ,' . $listas_string . '), ' : '') . ' RAND()', false, false);
			$query = $this->db->get();
		}

		return $query->result_array();
	}

	public function imoveis_oferecidos($cliente_id, $todos = false)
	{

		if ($todos) {
			// Incluir imóveis descartados
		} else {
			if (isset($_GET['descartados']) && $_GET['descartados'] == 1) {
				$this->db->where('IO.descartado', 1);
			} else {
				$this->db->where('IO.descartado', 0);
			}
		}

		$imoveis = $this->db->select('I.id, I.referencia, T.tipo, B.bairro, 
			I.valor_total, I.aluguel, I.tipo_venda, I.tipo_locacao, IO.descartado')
			->select('foto_principal(I.id) AS foto_principal', false)
			->from('imoveis I')
			->join('imoveis_oferecidos IO', 'IO.imovel_id = I.id')
			->join('bairros B', 'B.id = I.bairro_id', 'left')
			->join('tipos T', 'T.id = I.tipo_id')
			->where('IO.cliente_id', $cliente_id)
			->distinct()
			->get()
			->result_array();

		resetarConexao();
		return $imoveis;
	}

	public function set_alteracao($id = null)
	{
		if (isset($id) && $id != '') {
			$alteracao =  tratar_valor_para_bd('', 'timestamp');
			$this->db->query('UPDATE clientes SET alteracao=\'' . $alteracao . '\' WHERE id=' . $id);
			return true;
		} else {
			return false;
		}
	}
}
