# Cards Export Endpoint

## Descrição
A página `exportar/cards` foi criada para exportar dados de cards (leads do funil) em formato XML, seguindo o mesmo padrão das exportações de clientes e proprietários.

## Funcionalidade Implementada

### Controller
- **Arquivo**: `application/controllers/exportar.php`
- **Método**: `cards($chave = null)`
- **Linha**: 1135-1174

### View
- **Arquivo**: `application/views/exportar/cards.php`
- **Formato**: XML com estrutura hierárquica

### Rota
- **Configuração**: `application/config/routes.php` (linha 111)
- **Padrão**: `exportar/cards/(:any)` → `exportar/cards/$1`

## Como Usar

### URL de Acesso
```
http://seu-dominio.com/index.php/exportar/cards/{chave_da_imobiliaria}
```

### Parâmetros
- `{chave_da_imobiliaria}`: Chave única da imobiliária (obrigatório)

### Exemplo de Uso
```
http://localhost/imoviewweb/index.php/exportar/cards/4f2b344c5268b61fb41f14c640aeaf89bb7ae6d7
```

## Estrutura do XML Retornado

```xml
<?xml version="1.0" encoding="UTF-8"?>
<DataHora>2025-01-07 10:30:00</DataHora>
<Cards>
    <Card>
        <ID>123</ID>
        <Sequencial>2025001</Sequencial>
        <Titulo><![CDATA[João Silva - Facebook - #2025001]]></Titulo>
        <Mensagem><![CDATA[Cliente interessado em apartamento]]></Mensagem>
        <ValorPotencial>500000.00</ValorPotencial>
        <DataCadastro>2025-01-01 14:30:00</DataCadastro>
        <DataAlteracao>2025-01-07 10:15:00</DataAlteracao>
        <PrevisaoFechamento>2025-02-15</PrevisaoFechamento>
        <Ordem>1</Ordem>
        <TipoCliente>cliente</TipoCliente>
        <Cliente>
            <Nome><![CDATA[João Silva]]></Nome>
            <ID>456</ID>
        </Cliente>
        <Funil>
            <Tipo><![CDATA[Venda]]></Tipo>
            <TipoID>1</TipoID>
            <Etapa><![CDATA[Primeiro Contato]]></Etapa>
            <EtapaID>1</EtapaID>
        </Funil>
        <Status>
            <Nome><![CDATA[Em Andamento]]></Nome>
            <ID>2</ID>
        </Status>
        <Midia>
            <Tipo><![CDATA[Facebook]]></Tipo>
            <ID>3</ID>
        </Midia>
        <Responsaveis><![CDATA[Maria Santos, Pedro Costa]]></Responsaveis>
        <ImovelAngariado>
            <Referencia>REF001</Referencia>
            <ID>789</ID>
        </ImovelAngariado>
        <PedidoID>101</PedidoID>
        <ImobiliariaID>1</ImobiliariaID>
    </Card>
</Cards>
```

## Dados Incluídos

### Informações Básicas do Card
- ID único do card
- Sequencial do lead
- Título do card
- Mensagem/descrição
- Valor potencial
- Datas de cadastro, alteração e previsão de fechamento
- Ordem no funil

### Informações do Cliente
- Nome e ID do cliente
- Tipo de cliente (cliente/proprietário)

### Informações do Funil
- Tipo de funil (Venda, Locação, Angariação, etc.)
- Etapa atual no funil
- Status do atendimento

### Informações Adicionais
- Mídia de origem do lead
- Responsáveis pelo atendimento
- Imóvel angariado (se aplicável)
- ID do pedido relacionado
- ID da imobiliária

## Correções Implementadas

1. **Removida duplicação de JOIN**: Corrigido JOIN duplicado da tabela `statusdeatendimento`
2. **Adicionado GROUP BY**: Incluído `GROUP BY F.id` para evitar duplicações
3. **Estrutura XML consistente**: Seguindo o padrão das outras exportações

## Segurança

- Validação obrigatória da chave da imobiliária
- Filtro por imobiliária para garantir isolamento de dados
- Saída em XML com encoding UTF-8
- Uso de CDATA para campos de texto

## Observações

- A exportação retorna todos os cards/leads da imobiliária especificada
- Os dados são ordenados por ID do card
- Campos opcionais são tratados com verificação `isset()`
- Compatível com o padrão existente de exportações do sistema
